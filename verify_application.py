#!/usr/bin/env python3
"""
Quick Application Verification Script for CreatorVerse.

This script verifies that the application can start successfully after database setup.
It tests the core components without actually starting the full web server.
"""

import sys
import os
import asyncio
from pathlib import Path

# Add the app directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '.'))

async def test_application_startup():
    """Test core application components that were failing."""
    
    print("🧪 CreatorVerse Application Verification")
    print("=" * 50)
    
    try:
        # Test 1: Import core modules
        print("\n1. Testing module imports...")
        from app.core_helper.database import AsyncDatabaseDB
        from app.core_helper.async_logger import get_logger
        from app.models.filter_models import FilterGroup, FilterDefinition
        print("   ✅ Core modules imported successfully")
        
        # Test 2: Database connection
        print("\n2. Testing database connection...")
        db = AsyncDatabaseDB()
        db.set_connection_string("postgresql+asyncpg://postgres:s81JKkaoe42Tm5W@172.16.4.173:5432/postgres")
        await db.initialize()
        print("   ✅ Database connection established")
        
        # Test 3: Test the specific query that was failing
        print("\n3. Testing filter groups query (the failing operation)...")
        async with db.get_db() as session:
            from sqlalchemy import select
            from app.models.filter_models import FilterGroup
            
            # This is the exact query that was failing during startup
            stmt = select(FilterGroup).where(
                FilterGroup.channel == 'instagram',
                FilterGroup.option_for == 'creator',
                FilterGroup.is_active == True
            ).order_by(FilterGroup.sort_order, FilterGroup.name)
            
            result = await session.execute(stmt)
            groups = result.scalars().all()
            print(f"   ✅ Found {len(groups)} filter groups for Instagram creators")
            
            # Display some details
            for group in groups[:3]:  # Show first 3
                print(f"      - {group.name} (sort: {group.sort_order})")
        
        # Test 4: Test filter service (if it exists)
        print("\n4. Testing filter service...")
        try:
            from app.services.filter_service import get_filter_groups
            groups = await get_filter_groups('instagram', 'creator', session)
            print(f"   ✅ Filter service working - returned {len(groups)} groups")
        except ImportError:
            print("   ⚠️ Filter service not found (might be renamed or moved)")
        except Exception as e:
            print(f"   ❌ Filter service error: {e}")
            
        # Test 5: Verify enum usage
        print("\n5. Testing enum values...")
        from app.models.filter_models import PlatformTypeEnum, OptionForTypeEnum
        print(f"   ✅ Platform types: {[p.value for p in PlatformTypeEnum]}")
        print(f"   ✅ Option types: {[o.value for o in OptionForTypeEnum]}")
        
        await db.shutdown()
        
        print("\n" + "=" * 50)
        print("🎉 ALL TESTS PASSED!")
        print("✅ The application should now start successfully")
        print("\nNext steps:")
        print("1. Run: uvicorn main:app --port 8999")
        print("2. Check that startup completes without enum errors")
        print("3. Test the filter endpoints")
        
        return True
        
    except Exception as e:
        print(f"\n❌ VERIFICATION FAILED: {str(e)}")
        import traceback
        traceback.print_exc()
        
        print("\n🔧 TROUBLESHOOTING:")
        print("1. Ensure database setup completed: python enhanced_setup_database.py verify")
        print("2. Check database connectivity")
        print("3. Verify all enums and tables exist")
        
        return False

if __name__ == "__main__":
    success = asyncio.run(test_application_startup())
    sys.exit(0 if success else 1)
