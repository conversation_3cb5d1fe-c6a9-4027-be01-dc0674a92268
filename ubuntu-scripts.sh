#!/bin/bash

set -e

echo "🧽 Updating system..."
sudo apt update && sudo apt upgrade -y

echo "🛠️ Installing essentials..."
sudo apt install -y curl wget git build-essential software-properties-common ca-certificates apt-transport-https gnupg lsb-release unzip

echo "💡 Installing ZSH and Oh My Zsh..."
sudo apt install -y zsh
chsh -s $(which zsh)
sh -c "$(curl -fsSL https://raw.githubusercontent.com/ohmyzsh/ohmyzsh/master/tools/install.sh)"

echo "🎨 Installing Powerlevel10k theme..."
git clone --depth=1 https://github.com/romkatv/powerlevel10k.git ${ZSH_CUSTOM:-$HOME/.oh-my-zsh/custom}/themes/powerlevel10k
sed -i 's/^ZSH_THEME=.*/ZSH_THEME="powerlevel10k\/powerlevel10k"/' ~/.zshrc

echo "🚀 Installing NVIDIA drivers (auto-detect)..."
sudo ubuntu-drivers autoinstall

echo "🔐 Installing OpenVPN..."
sudo apt install -y openvpn

echo "🧶 Installing Node.js 22 and pnpm..."
curl -fsSL https://deb.nodesource.com/setup_22.x | sudo -E bash -
sudo apt install -y nodejs
sudo npm install -g pnpm

echo "🐍 Installing uv (Python package manager)..."
curl -Ls https://astral.sh/uv/install.sh | bash


echo "📬 Installing Postman (via Snap)..."
sudo snap install postman

echo "🎞️ Installing VLC media player..."
sudo apt install -y vlc

echo "🎥 Installing FFmpeg..."
sudo apt install -y ffmpeg

echo "🎧 Installing additional media tools and codecs..."
sudo apt install -y ubuntu-restricted-extras \
    gstreamer1.0-plugins-base \
    gstreamer1.0-plugins-good \
    gstreamer1.0-plugins-bad \
    gstreamer1.0-plugins-ugly \
    gstreamer1.0-libav \
    libavcodec-extra \
    libdvd-pkg

echo "📀 Enabling DVD playback..."
sudo dpkg-reconfigure libdvd-pkg

echo "✅ All set! Please reboot your system to apply all changes."

