#!/usr/bin/env python3
"""
Filter System Demo and Quick Test Runner for CreatorVerse Profile Analytics.

This script demonstrates the complete filter system functionality and runs
quick tests to verify everything is working correctly.

Usage:
    python filter_demo.py --demo        # Run full demo
    python filter_demo.py --quick-test  # Run quick validation tests
    python filter_demo.py --setup       # Setup sample data and test
    python filter_demo.py --all         # Run everything
"""

import asyncio
import argparse
import json
import sys
import uuid
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.models.filter_models import PlatformTypeEnum, OptionForTypeEnum, FilterTypeEnum
from app.services.filter_service import FilterService
from app.schemas.filter_schemas import SaveFilterSetRequest, GetFiltersRequest
from scripts.filter_manager import FilterManager


class FilterSystemDemo:
    """Comprehensive filter system demonstration."""
    
    def __init__(self):
        self.filter_service = FilterService()
        self.filter_manager = FilterManager()
        self.demo_data = {}
    
    async def run_full_demo(self):
        """Run complete filter system demonstration."""
        print("🎬 CreatorVerse Filter System - Complete Demo")
        print("=" * 60)
        
        try:
            # Step 1: System Health Check
            print("\n📋 Step 1: System Health Check")
            await self._demo_health_check()
            
            # Step 2: Setup Sample Data
            print("\n📋 Step 2: Setting up Sample Data")
            await self._demo_data_setup()
            
            # Step 3: Retrieve Filters
            print("\n📋 Step 3: Retrieving Filter Configurations")
            await self._demo_filter_retrieval()
            
            # Step 4: Location Hierarchy
            print("\n📋 Step 4: Location Hierarchy Demo")
            await self._demo_location_hierarchy()
            
            # Step 5: Save and Retrieve Filter Sets
            print("\n📋 Step 5: Saved Filter Sets Demo")
            await self._demo_saved_filter_sets()
            
            # Step 6: Filter Management
            print("\n📋 Step 6: Filter Management Demo")
            await self._demo_filter_management()
            
            # Step 7: Analytics Demo
            print("\n📋 Step 7: Filter Analytics Demo")
            await self._demo_analytics()
            
            print("\n🎉 Demo completed successfully!")
            print("✅ All filter system components are working correctly")
            
        except Exception as e:
            print(f"\n❌ Demo failed: {str(e)}")
            raise
    
    async def _demo_health_check(self):
        """Demonstrate system health check."""
        print("🏥 Performing system health check...")
        
        try:
            health_data = await self.filter_manager.health_check()
            
            if health_data.get("overall_status") == "healthy":
                print("✅ System is healthy")
            else:
                print(f"⚠️  System status: {health_data.get('overall_status')}")
            
            # Show key metrics
            checks = health_data.get("checks", {})
            if "metadata" in checks:
                metadata = checks["metadata"]
                print(f"📊 {metadata.get('active_groups', 0)} active groups, {metadata.get('active_filters', 0)} active filters")
            
        except Exception as e:
            print(f"❌ Health check failed: {str(e)}")
    
    async def _demo_data_setup(self):
        """Demonstrate setting up sample data."""
        print("🌱 Setting up sample filter data...")
        
        try:
            # Check if we already have data
            metadata = await self.filter_service.get_filter_metadata()
            
            if metadata.total_groups > 0:
                print(f"📋 Found existing data: {metadata.total_groups} groups, {metadata.total_filters} filters")
                print("⏭️  Skipping data setup (already exists)")
                return
            
            # Seed sample data
            success = await self.filter_manager.seed_sample_data()
            
            if success:
                print("✅ Sample data created successfully")
                
                # Verify data was created
                new_metadata = await self.filter_service.get_filter_metadata()
                print(f"📊 Created: {new_metadata.total_groups} groups, {new_metadata.total_filters} filters")
            else:
                print("❌ Failed to create sample data")
                
        except Exception as e:
            print(f"❌ Data setup failed: {str(e)}")
    
    async def _demo_filter_retrieval(self):
        """Demonstrate retrieving filter configurations."""
        print("🔍 Retrieving filter configurations...")
        
        try:
            # Get Instagram creator filters
            instagram_creator_filters = await self.filter_service.get_filter_groups(
                channel=PlatformTypeEnum.instagram,
                option_for=OptionForTypeEnum.creator
            )
            
            print(f"📸 Instagram Creator Filters: {len(instagram_creator_filters)} groups")
            
            for group in instagram_creator_filters:
                print(f"   📁 {group.optionName}: {len(group.filters)} filters")
                for filter_def in group.filters[:2]:  # Show first 2 filters
                    print(f"      🔍 {filter_def.name} ({filter_def.filter_type.value})")
                    if len(filter_def.options) > 0:
                        print(f"         Options: {len(filter_def.options)} choices")
            
            # Store for later use
            self.demo_data['instagram_creator_filters'] = instagram_creator_filters
            
            # Get YouTube creator filters
            try:
                youtube_creator_filters = await self.filter_service.get_filter_groups(
                    channel=PlatformTypeEnum.youtube,
                    option_for=OptionForTypeEnum.creator
                )
                print(f"📺 YouTube Creator Filters: {len(youtube_creator_filters)} groups")
                self.demo_data['youtube_creator_filters'] = youtube_creator_filters
            except:
                print("📺 YouTube Creator Filters: Not configured yet")
            
        except Exception as e:
            print(f"❌ Filter retrieval failed: {str(e)}")
    
    async def _demo_location_hierarchy(self):
        """Demonstrate location hierarchy functionality."""
        print("📍 Demonstrating location hierarchy...")
        
        try:
            # Get root level locations (countries)
            countries = await self.filter_service.get_location_hierarchy(level=0)
            print(f"🌍 Found {len(countries)} countries")
            
            if countries:
                # Get states for first country
                first_country = countries[0]
                print(f"   Selected country: {first_country.name}")
                
                states = await self.filter_service.get_location_hierarchy(
                    parent_id=first_country.id,
                    level=1
                )
                print(f"   📍 States/Regions: {len(states)}")
                
                if states:
                    # Get cities for first state
                    first_state = states[0]
                    print(f"      Selected state: {first_state.name}")
                    
                    cities = await self.filter_service.get_location_hierarchy(
                        parent_id=first_state.id,
                        level=2
                    )
                    print(f"      🏙️  Cities: {len(cities)}")
                    
                    # Show tier-based filtering
                    tier1_cities = await self.filter_service.get_location_hierarchy(
                        tier="Tier 1",
                        level=2
                    )
                    print(f"   🏙️  Tier 1 cities: {len(tier1_cities)}")
            
        except Exception as e:
            print(f"❌ Location hierarchy demo failed: {str(e)}")
    
    async def _demo_saved_filter_sets(self):
        """Demonstrate saved filter sets functionality."""
        print("💾 Demonstrating saved filter sets...")
        
        try:
            # Create a sample filter set
            sample_filters = {
                "gender": "male",
                "age": ["20-35", "36-55"],
                "follower_count": ["10k-50k", "50k-500k"],
                "location": ["mumbai", "delhi"]
            }
            
            save_request = SaveFilterSetRequest(
                name="Demo Filter Set - Tech Male Influencers",
                channel=PlatformTypeEnum.instagram,
                option_for=OptionForTypeEnum.creator,
                filter_values=sample_filters,
                is_shared=True
            )
            
            # Save the filter set
            saved_set = await self.filter_service.save_filter_set(save_request)
            print(f"💾 Saved filter set: '{saved_set.name}'")
            print(f"   🔗 Share code: {saved_set.share_code}")
            print(f"   📊 Filter values: {len(saved_set.filter_values)} filters applied")
            
            # Retrieve saved filter sets
            saved_sets = await self.filter_service.get_saved_filter_sets(
                channel=PlatformTypeEnum.instagram,
                include_shared=True
            )
            print(f"📋 Found {len(saved_sets)} saved filter sets")
            
            for fs in saved_sets[:3]:  # Show first 3
                share_status = "Shared" if fs.is_shared else "Private"
                print(f"   💾 {fs.name} ({share_status}) - Used {fs.usage_count} times")
            
            # Store for cleanup
            self.demo_data['demo_filter_set_id'] = saved_set.id
            
        except Exception as e:
            print(f"❌ Saved filter sets demo failed: {str(e)}")
    
    async def _demo_filter_management(self):
        """Demonstrate filter management capabilities."""
        print("⚙️ Demonstrating filter management...")
        
        try:
            # List all channels
            print("📋 Listing all channels:")
            channels = await self.filter_manager.list_channels()
            
            for channel in channels:
                status = "🟢 Active" if channel["groups_enabled"] else "🔴 Inactive"
                print(f"   📺 {channel['channel'].upper()}: {status}")
                print(f"      Groups: {channel['active_groups']}/{channel['total_groups']}")
                print(f"      Filters: {channel['active_filters']}/{channel['total_filters']}")
            
            # Demonstrate adding a new filter group (test)
            print("\n🆕 Testing filter group creation:")
            test_group_id = await self.filter_manager.add_filter_group(
                name="Demo Test Group",
                channel="instagram",
                option_for="creator",
                sort_order=99
            )
            
            if test_group_id:
                print(f"✅ Created test group: {test_group_id}")
                
                # Add a test filter to the group
                test_filter_id = await self.filter_manager.add_filter_definition(
                    group_id=test_group_id,
                    name="Test Filter",
                    filter_type="checkbox",
                    icon="test-icon",
                    options=[
                        {"label": "Test Option 1", "value": "test1", "description": "Test option 1"},
                        {"label": "Test Option 2", "value": "test2", "description": "Test option 2"}
                    ],
                    db_field="test_field",
                    api_field="test_api_field"
                )
                
                if test_filter_id:
                    print(f"✅ Created test filter: {test_filter_id}")
                    self.demo_data['test_group_id'] = test_group_id
                    self.demo_data['test_filter_id'] = test_filter_id
            
        except Exception as e:
            print(f"❌ Filter management demo failed: {str(e)}")
    
    async def _demo_analytics(self):
        """Demonstrate filter analytics and usage tracking."""
        print("📊 Demonstrating filter analytics...")
        
        try:
            # Get system metadata
            metadata = await self.filter_service.get_filter_metadata()
            
            print("📈 System Statistics:")
            print(f"   📁 Total Groups: {metadata.total_groups} ({metadata.active_groups} active)")
            print(f"   🔍 Total Filters: {metadata.total_filters} ({metadata.active_filters} active)")
            print(f"   📺 Platform Breakdown:")
            
            for platform, count in metadata.platform_breakdown.items():
                print(f"      {platform}: {count} groups")
            
            print(f"   🕒 Last Updated: {metadata.last_updated}")
            
            # Simulate filter usage logging
            if self.demo_data.get('test_filter_id'):
                print("\n📝 Simulating filter usage logging...")
                
                await self.filter_service.log_filter_usage(
                    filter_definition_id=uuid.UUID(self.demo_data['test_filter_id']),
                    filter_value={"selected": ["test1", "test2"]},
                    result_count=150,
                    execution_time_ms=75,
                    session_id="demo_session_123"
                )
                
                print("✅ Logged filter usage for analytics")
            
        except Exception as e:
            print(f"❌ Analytics demo failed: {str(e)}")
    
    async def run_quick_tests(self):
        """Run quick validation tests."""
        print("🧪 CreatorVerse Filter System - Quick Tests")
        print("=" * 50)
        
        tests_passed = 0
        tests_total = 0
        
        # Test 1: Database connectivity
        tests_total += 1
        print("🧪 Test 1: Database Connectivity")
        try:
            metadata = await self.filter_service.get_filter_metadata()
            print("✅ Database connected and responsive")
            tests_passed += 1
        except Exception as e:
            print(f"❌ Database test failed: {str(e)}")
        
        # Test 2: Filter retrieval
        tests_total += 1
        print("\n🧪 Test 2: Filter Retrieval")
        try:
            filters = await self.filter_service.get_filter_groups(
                channel=PlatformTypeEnum.instagram,
                option_for=OptionForTypeEnum.creator
            )
            print(f"✅ Retrieved {len(filters)} filter groups")
            tests_passed += 1
        except Exception as e:
            print(f"❌ Filter retrieval test failed: {str(e)}")
        
        # Test 3: Location hierarchy
        tests_total += 1
        print("\n🧪 Test 3: Location Hierarchy")
        try:
            locations = await self.filter_service.get_location_hierarchy()
            print(f"✅ Retrieved {len(locations)} locations")
            tests_passed += 1
        except Exception as e:
            print(f"❌ Location hierarchy test failed: {str(e)}")
        
        # Test 4: Cache connectivity
        tests_total += 1
        print("\n🧪 Test 4: Cache Connectivity")
        try:
            await self.filter_service.redis.ping()
            print("✅ Redis cache connected")
            tests_passed += 1
        except Exception as e:
            print(f"❌ Cache test failed: {str(e)}")
        
        # Test 5: Filter save/retrieve
        tests_total += 1
        print("\n🧪 Test 5: Filter Set Operations")
        try:
            # Save a test filter set
            test_request = SaveFilterSetRequest(
                name="Quick Test Filter Set",
                channel=PlatformTypeEnum.instagram,
                option_for=OptionForTypeEnum.creator,
                filter_values={"test": "value"},
                is_shared=False
            )
            
            saved = await self.filter_service.save_filter_set(test_request)
            print(f"✅ Saved and retrieved filter set: {saved.name}")
            tests_passed += 1
        except Exception as e:
            print(f"❌ Filter set operations test failed: {str(e)}")
        
        # Results summary
        print(f"\n📊 Test Results: {tests_passed}/{tests_total} tests passed")
        
        if tests_passed == tests_total:
            print("🎉 All tests passed! Filter system is working correctly.")
            return True
        else:
            print("⚠️  Some tests failed. Check the system configuration.")
            return False
    
    async def setup_and_test(self):
        """Setup sample data and run quick tests."""
        print("🔧 CreatorVerse Filter System - Setup & Test")
        print("=" * 50)
        
        try:
            # Setup sample data
            print("🌱 Setting up sample data...")
            await self.filter_manager.seed_sample_data()
            
            # Run quick tests
            print("\n🧪 Running validation tests...")
            success = await self.run_quick_tests()
            
            if success:
                print("\n✅ Setup and testing completed successfully!")
                print("🚀 Your filter system is ready to use!")
            else:
                print("\n⚠️  Setup completed but some tests failed.")
                print("🔍 Please check the system configuration.")
            
        except Exception as e:
            print(f"\n❌ Setup and test failed: {str(e)}")
    
    async def cleanup_demo_data(self):
        """Clean up demo data created during testing."""
        print("🧹 Cleaning up demo data...")
        
        try:
            # This would clean up test data created during demo
            # For now, just print what would be cleaned
            print("ℹ️  Demo cleanup not implemented (data will remain for inspection)")
            print("   You can manually clean test data using the filter_manager.py CLI")
            
        except Exception as e:
            print(f"❌ Cleanup failed: {str(e)}")


async def main():
    """Main entry point for filter demo."""
    parser = argparse.ArgumentParser(
        description="CreatorVerse Filter System Demo and Test Runner",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s --demo        # Run full system demonstration
  %(prog)s --quick-test  # Run quick validation tests
  %(prog)s --setup       # Setup sample data and test
  %(prog)s --all         # Run complete demo with setup
        """
    )
    
    parser.add_argument('--demo', action='store_true', help='Run full system demonstration')
    parser.add_argument('--quick-test', action='store_true', help='Run quick validation tests')
    parser.add_argument('--setup', action='store_true', help='Setup sample data and test')
    parser.add_argument('--all', action='store_true', help='Run everything (setup + demo + tests)')
    parser.add_argument('--cleanup', action='store_true', help='Clean up demo data')
    
    args = parser.parse_args()
    
    if not any([args.demo, args.quick_test, args.setup, args.all, args.cleanup]):
        parser.print_help()
        return
    
    demo = FilterSystemDemo()
    
    try:
        if args.cleanup:
            await demo.cleanup_demo_data()
        
        elif args.all:
            print("🎬 Running Complete Filter System Demo")
            print("=" * 60)
            
            await demo.setup_and_test()
            print("\n" + "=" * 60)
            await demo.run_full_demo()
        
        elif args.setup:
            await demo.setup_and_test()
        
        elif args.demo:
            await demo.run_full_demo()
        
        elif args.quick_test:
            success = await demo.run_quick_tests()
            return 0 if success else 1
    
    except KeyboardInterrupt:
        print("\n⚠️  Demo interrupted by user")
        return 1
    except Exception as e:
        print(f"\n❌ Demo failed with error: {str(e)}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
