#!/usr/bin/env python3
"""
Filter System Management CLI for CreatorVerse Profile Analytics.

This comprehensive command-line tool provides management capabilities for:
- Adding and managing filter groups and definitions
- Adding new channels (platforms) to the system
- Enabling/disabling filters and channels
- Managing location hierarchy
- Database seeding and maintenance
- System health checks and analytics

Usage:
    python filter_manager.py --help
    python filter_manager.py add-channel --name snapchat --display "Snapchat"
    python filter_manager.py add-filter-group --name "Brand Affinity" --channel instagram --option-for creator
    python filter_manager.py add-filter --group-id <uuid> --name "Brand Mentions" --type checkbox
    python filter_manager.py disable-channel --channel tiktok
    python filter_manager.py list-channels
    python filter_manager.py health-check
"""

import asyncio
import argparse
import json
import sys
import uuid
from datetime import datetime
from typing import Dict, Any, List, Optional
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy import select, update, text, func
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.filter_models import (
    FilterGroup, FilterDefinition, LocationHierarchy, SavedFilterSet,
    PlatformTypeEnum, OptionForTypeEnum, FilterTypeEnum
)
from app.services.filter_service import FilterService
from app.core.config import get_database


class FilterManager:
    """Main filter management class."""
    
    def __init__(self):
        self.db = get_database()
        self.filter_service = FilterService()
    
    async def add_new_channel(self, channel_name: str, display_name: str) -> bool:
        """
        Add a new platform/channel to the system.
        
        Args:
            channel_name: Internal name for the channel (e.g., 'snapchat')
            display_name: Display name for the channel (e.g., 'Snapchat')
            
        Returns:
            True if successful
        """
        try:
            print(f"🔧 Adding new channel: {channel_name} ({display_name})")
            
            # Step 1: Add to enum type in database
            async with self.db.get_session() as session:
                # Check if enum value already exists
                check_query = text("""
                    SELECT 1 FROM pg_enum 
                    WHERE enumtypid = (
                        SELECT oid FROM pg_type WHERE typname = 'platform_type'
                    ) AND enumlabel = :channel_name
                """)
                result = await session.execute(check_query, {"channel_name": channel_name})
                
                if result.fetchone():
                    print(f"❌ Channel '{channel_name}' already exists in database enum")
                    return False
                
                # Add new enum value
                add_enum_query = text(f"""
                    ALTER TYPE filter_catalog.platform_type ADD VALUE '{channel_name}'
                """)
                await session.execute(add_enum_query)
                await session.commit()
                
                print(f"✅ Added '{channel_name}' to platform_type enum")
            
            # Step 2: Update Python enum (requires code change)
            print(f"⚠️  Manual step required:")
            print(f"   Add '{channel_name} = \"{channel_name}\"' to PlatformTypeEnum in:")
            print(f"   - app/models/filter_models.py")
            print(f"   - app/schemas/filter_schemas.py")
            
            # Step 3: Create basic filter groups for the new channel
            await self._create_default_filter_groups(channel_name)
            
            print(f"🎉 Channel '{channel_name}' added successfully!")
            print(f"📝 Remember to restart the application after updating the Python enums")
            
            return True
            
        except Exception as e:
            print(f"❌ Error adding channel: {str(e)}")
            return False
    
    async def _create_default_filter_groups(self, channel: str) -> None:
        """Create default filter groups for a new channel."""
        default_groups = [
            {
                "name": "Demography & Identity", 
                "option_for": "creator",
                "sort_order": 1
            },
            {
                "name": "Demography & Identity", 
                "option_for": "audience",
                "sort_order": 1
            },
            {
                "name": "Performance Metrics", 
                "option_for": "creator",
                "sort_order": 2
            },
            {
                "name": "Performance Metrics", 
                "option_for": "audience",
                "sort_order": 2
            },
            {
                "name": "Content & Niche", 
                "option_for": "creator",
                "sort_order": 3
            },
            {
                "name": "Credibility & Platform", 
                "option_for": "creator",
                "sort_order": 4
            }
        ]
        
        async with self.db.get_session() as session:
            for group_data in default_groups:
                # Check if group already exists
                existing_query = select(FilterGroup).where(
                    FilterGroup.name == group_data["name"],
                    FilterGroup.channel == channel,
                    FilterGroup.option_for == group_data["option_for"]
                )
                existing = await session.execute(existing_query)
                
                if not existing.scalar_one_or_none():
                    new_group = FilterGroup(
                        name=group_data["name"],
                        option_for=OptionForTypeEnum(group_data["option_for"]),
                        channel=channel,  # This will need the enum to be updated
                        sort_order=group_data["sort_order"]
                    )
                    session.add(new_group)
                    print(f"   📁 Created group: {group_data['name']} ({group_data['option_for']})")
            
            await session.commit()
    
    async def add_filter_group(
        self, 
        name: str, 
        channel: str, 
        option_for: str,
        sort_order: int = 0
    ) -> Optional[str]:
        """
        Add a new filter group.
        
        Args:
            name: Display name for the group
            channel: Platform/channel name
            option_for: 'creator' or 'audience'
            sort_order: Display order
            
        Returns:
            Group ID if successful, None otherwise
        """
        try:
            print(f"📁 Adding filter group: {name}")
            
            async with self.db.get_session() as session:
                # Check if group already exists
                existing_query = select(FilterGroup).where(
                    FilterGroup.name == name,
                    FilterGroup.channel == PlatformTypeEnum(channel),
                    FilterGroup.option_for == OptionForTypeEnum(option_for)
                )
                existing = await session.execute(existing_query)
                
                if existing.scalar_one_or_none():
                    print(f"❌ Filter group '{name}' already exists for {channel}:{option_for}")
                    return None
                
                # Create new group
                new_group = FilterGroup(
                    name=name,
                    channel=PlatformTypeEnum(channel),
                    option_for=OptionForTypeEnum(option_for),
                    sort_order=sort_order
                )
                
                session.add(new_group)
                await session.commit()
                await session.refresh(new_group)
                
                print(f"✅ Created filter group: {name} (ID: {new_group.id})")
                return str(new_group.id)
                
        except Exception as e:
            print(f"❌ Error adding filter group: {str(e)}")
            return None
    
    async def add_filter_definition(
        self,
        group_id: str,
        name: str,
        filter_type: str,
        icon: Optional[str] = None,
        has_minmax: bool = False,
        has_enter_value: bool = False,
        has_search_box: bool = False,
        placeholder: Optional[str] = None,
        options: Optional[List[Dict[str, str]]] = None,
        db_field: Optional[str] = None,
        api_field: Optional[str] = None,
        sort_order: int = 0
    ) -> Optional[str]:
        """
        Add a new filter definition to a group.
        
        Args:
            group_id: ID of the parent group
            name: Display name for the filter
            filter_type: Type of filter UI component
            icon: Icon name/class
            has_minmax: Whether filter supports min/max
            has_enter_value: Whether filter allows text input
            has_search_box: Whether filter has search
            placeholder: Placeholder text
            options: List of filter options
            db_field: Database field mapping
            api_field: API field mapping
            sort_order: Display order
            
        Returns:
            Filter ID if successful, None otherwise
        """
        try:
            print(f"🔍 Adding filter: {name}")
            
            async with self.db.get_session() as session:
                # Verify group exists
                group_query = select(FilterGroup).where(FilterGroup.id == uuid.UUID(group_id))
                group_result = await session.execute(group_query)
                group = group_result.scalar_one_or_none()
                
                if not group:
                    print(f"❌ Filter group with ID {group_id} not found")
                    return None
                
                # Check if filter already exists in group
                existing_query = select(FilterDefinition).where(
                    FilterDefinition.group_id == uuid.UUID(group_id),
                    FilterDefinition.name == name
                )
                existing = await session.execute(existing_query)
                
                if existing.scalar_one_or_none():
                    print(f"❌ Filter '{name}' already exists in group '{group.name}'")
                    return None
                
                # Create new filter
                new_filter = FilterDefinition(
                    group_id=uuid.UUID(group_id),
                    name=name,
                    filter_type=FilterTypeEnum(filter_type),
                    icon=icon,
                    has_minmax=has_minmax,
                    has_enter_value=has_enter_value,
                    has_search_box=has_search_box,
                    placeholder=placeholder,
                    options=options or [],
                    db_field=db_field,
                    api_field=api_field,
                    sort_order=sort_order
                )
                
                session.add(new_filter)
                await session.commit()
                await session.refresh(new_filter)
                
                print(f"✅ Created filter: {name} (ID: {new_filter.id})")
                print(f"   📋 Type: {filter_type}")
                print(f"   📁 Group: {group.name}")
                print(f"   📊 Options: {len(options or [])}")
                
                return str(new_filter.id)
                
        except Exception as e:
            print(f"❌ Error adding filter: {str(e)}")
            return None
    
    async def toggle_channel_status(self, channel: str, is_active: bool) -> bool:
        """
        Enable or disable all filters for a channel.
        
        Args:
            channel: Platform/channel name
            is_active: New active status
            
        Returns:
            True if successful
        """
        try:
            print(f"🔧 {'Enabling' if is_active else 'Disabling'} channel: {channel}")
            
            async with self.db.get_session() as session:
                # Update all groups for the channel
                groups_query = update(FilterGroup).where(
                    FilterGroup.channel == PlatformTypeEnum(channel)
                ).values(is_active=is_active)
                
                groups_result = await session.execute(groups_query)
                
                # Update all filters for the channel
                filters_query = text("""
                    UPDATE filter_catalog.filter_definitions 
                    SET is_active = :is_active, updated_at = CURRENT_TIMESTAMP
                    WHERE group_id IN (
                        SELECT id FROM filter_catalog.filter_groups 
                        WHERE channel = :channel
                    )
                """)
                
                await session.execute(filters_query, {
                    "is_active": is_active,
                    "channel": channel
                })
                
                await session.commit()
                
                affected_groups = groups_result.rowcount
                print(f"✅ {'Enabled' if is_active else 'Disabled'} {affected_groups} groups and their filters for {channel}")
                
                return True
                
        except Exception as e:
            print(f"❌ Error toggling channel status: {str(e)}")
            return False
    
    async def list_channels(self) -> List[Dict[str, Any]]:
        """
        List all channels with their status and statistics.
        
        Returns:
            List of channel information
        """
        try:
            print("📋 Listing all channels:")
            print("=" * 60)
            
            async with self.db.get_session() as session:
                # Get channel statistics
                stats_query = text("""
                    SELECT 
                        fg.channel,
                        COUNT(DISTINCT fg.id) as total_groups,
                        COUNT(DISTINCT CASE WHEN fg.is_active THEN fg.id END) as active_groups,
                        COUNT(DISTINCT fd.id) as total_filters,
                        COUNT(DISTINCT CASE WHEN fd.is_active THEN fd.id END) as active_filters
                    FROM filter_catalog.filter_groups fg
                    LEFT JOIN filter_catalog.filter_definitions fd ON fg.id = fd.group_id
                    GROUP BY fg.channel
                    ORDER BY fg.channel
                """)
                
                result = await session.execute(stats_query)
                channels = []
                
                for row in result:
                    channel_info = {
                        "channel": row.channel,
                        "total_groups": row.total_groups,
                        "active_groups": row.active_groups,
                        "total_filters": row.total_filters,
                        "active_filters": row.active_filters,
                        "groups_enabled": row.active_groups > 0,
                        "filters_enabled": row.active_filters > 0
                    }
                    channels.append(channel_info)
                    
                    # Print channel info
                    status_icon = "🟢" if channel_info["groups_enabled"] else "🔴"
                    print(f"{status_icon} {row.channel.upper()}")
                    print(f"   📁 Groups: {row.active_groups}/{row.total_groups} active")
                    print(f"   🔍 Filters: {row.active_filters}/{row.total_filters} active")
                    print()
                
                return channels
                
        except Exception as e:
            print(f"❌ Error listing channels: {str(e)}")
            return []
    
    async def list_filter_groups(self, channel: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        List all filter groups with their details.
        
        Args:
            channel: Optional channel filter
            
        Returns:
            List of filter group information
        """
        try:
            print(f"📁 Listing filter groups{' for ' + channel if channel else ''}:")
            print("=" * 60)
            
            async with self.db.get_session() as session:
                query = select(FilterGroup).order_by(
                    FilterGroup.channel, 
                    FilterGroup.option_for, 
                    FilterGroup.sort_order
                )
                
                if channel:
                    query = query.where(FilterGroup.channel == PlatformTypeEnum(channel))
                
                result = await session.execute(query)
                groups = result.scalars().all()
                
                group_list = []
                current_channel = None
                
                for group in groups:
                    if current_channel != group.channel.value:
                        current_channel = group.channel.value
                        print(f"\n📺 {current_channel.upper()}")
                        print("-" * 40)
                    
                    group_info = {
                        "id": str(group.id),
                        "name": group.name,
                        "channel": group.channel.value,
                        "option_for": group.option_for.value,
                        "sort_order": group.sort_order,
                        "is_active": group.is_active,
                        "created_at": group.created_at
                    }
                    group_list.append(group_info)
                    
                    status_icon = "🟢" if group.is_active else "🔴"
                    print(f"  {status_icon} {group.name} ({group.option_for.value}) - Order: {group.sort_order}")
                    print(f"     ID: {group.id}")
                
                print(f"\n📊 Total: {len(groups)} groups")
                return group_list
                
        except Exception as e:
            print(f"❌ Error listing filter groups: {str(e)}")
            return []
    
    async def list_filters(self, group_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        List filter definitions.
        
        Args:
            group_id: Optional group ID filter
            
        Returns:
            List of filter information
        """
        try:
            print(f"🔍 Listing filters{' for group ' + group_id if group_id else ''}:")
            print("=" * 60)
            
            async with self.db.get_session() as session:
                if group_id:
                    # Get specific group's filters
                    query = select(FilterDefinition).where(
                        FilterDefinition.group_id == uuid.UUID(group_id)
                    ).order_by(FilterDefinition.sort_order, FilterDefinition.name)
                else:
                    # Get all filters with group information
                    from sqlalchemy.orm import selectinload
                    query = select(FilterDefinition).options(
                        selectinload(FilterDefinition.group)
                    ).order_by(FilterDefinition.group_id, FilterDefinition.sort_order)
                
                result = await session.execute(query)
                filters = result.scalars().all()
                
                filter_list = []
                current_group = None
                
                for filter_def in filters:
                    if not group_id and current_group != filter_def.group_id:
                        current_group = filter_def.group_id
                        print(f"\n📁 Group: {filter_def.group.name}")
                        print("-" * 40)
                    
                    filter_info = {
                        "id": str(filter_def.id),
                        "name": filter_def.name,
                        "type": filter_def.filter_type.value,
                        "group_id": str(filter_def.group_id),
                        "is_active": filter_def.is_active,
                        "has_minmax": filter_def.has_minmax,
                        "has_enter_value": filter_def.has_enter_value,
                        "options_count": len(filter_def.options),
                        "db_field": filter_def.db_field,
                        "api_field": filter_def.api_field
                    }
                    filter_list.append(filter_info)
                    
                    status_icon = "🟢" if filter_def.is_active else "🔴"
                    print(f"  {status_icon} {filter_def.name} ({filter_def.filter_type.value})")
                    print(f"     ID: {filter_def.id}")
                    print(f"     Options: {len(filter_def.options)}")
                    if filter_def.db_field:
                        print(f"     DB Field: {filter_def.db_field}")
                    if filter_def.api_field:
                        print(f"     API Field: {filter_def.api_field}")
                
                print(f"\n📊 Total: {len(filters)} filters")
                return filter_list
                
        except Exception as e:
            print(f"❌ Error listing filters: {str(e)}")
            return []
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Perform comprehensive health check of the filter system.
        
        Returns:
            Health check results
        """
        try:
            print("🏥 Performing filter system health check...")
            print("=" * 60)
            
            health_data = {
                "timestamp": datetime.utcnow().isoformat(),
                "overall_status": "healthy",
                "checks": {}
            }
            
            async with self.db.get_session() as session:
                # Check 1: Database connectivity
                try:
                    await session.execute(text("SELECT 1"))
                    health_data["checks"]["database"] = {"status": "healthy", "message": "Connected"}
                    print("✅ Database: Connected")
                except Exception as e:
                    health_data["checks"]["database"] = {"status": "error", "message": str(e)}
                    print(f"❌ Database: {str(e)}")
                    health_data["overall_status"] = "unhealthy"
                
                # Check 2: Filter metadata
                try:
                    metadata = await self.filter_service.get_filter_metadata()
                    health_data["checks"]["metadata"] = {
                        "status": "healthy",
                        "total_groups": metadata.total_groups,
                        "active_groups": metadata.active_groups,
                        "total_filters": metadata.total_filters,
                        "active_filters": metadata.active_filters
                    }
                    print(f"✅ Metadata: {metadata.active_groups}/{metadata.total_groups} groups, {metadata.active_filters}/{metadata.total_filters} filters active")
                except Exception as e:
                    health_data["checks"]["metadata"] = {"status": "error", "message": str(e)}
                    print(f"❌ Metadata: {str(e)}")
                    health_data["overall_status"] = "degraded"
                
                # Check 3: Enum consistency
                try:
                    enum_check_query = text("""
                        SELECT enumlabel 
                        FROM pg_enum 
                        WHERE enumtypid = (
                            SELECT oid FROM pg_type WHERE typname = 'platform_type'
                        )
                        ORDER BY enumlabel
                    """)
                    result = await session.execute(enum_check_query)
                    db_platforms = [row.enumlabel for row in result]
                    
                    python_platforms = [e.value for e in PlatformTypeEnum]
                    
                    if set(db_platforms) == set(python_platforms):
                        health_data["checks"]["enum_consistency"] = {"status": "healthy", "platforms": db_platforms}
                        print(f"✅ Enums: Consistent ({len(db_platforms)} platforms)")
                    else:
                        health_data["checks"]["enum_consistency"] = {
                            "status": "warning",
                            "db_platforms": db_platforms,
                            "python_platforms": python_platforms,
                            "message": "Platform enum mismatch between database and Python"
                        }
                        print(f"⚠️  Enums: Mismatch detected")
                        print(f"   DB: {db_platforms}")
                        print(f"   Python: {python_platforms}")
                        if health_data["overall_status"] == "healthy":
                            health_data["overall_status"] = "degraded"
                
                except Exception as e:
                    health_data["checks"]["enum_consistency"] = {"status": "error", "message": str(e)}
                    print(f"❌ Enum Check: {str(e)}")
                
                # Check 4: Orphaned records
                try:
                    orphaned_filters_query = text("""
                        SELECT COUNT(*) as count
                        FROM filter_catalog.filter_definitions fd
                        LEFT JOIN filter_catalog.filter_groups fg ON fd.group_id = fg.id
                        WHERE fg.id IS NULL
                    """)
                    result = await session.execute(orphaned_filters_query)
                    orphaned_count = result.scalar()
                    
                    if orphaned_count == 0:
                        health_data["checks"]["data_integrity"] = {"status": "healthy", "orphaned_filters": 0}
                        print("✅ Data Integrity: No orphaned records")
                    else:
                        health_data["checks"]["data_integrity"] = {
                            "status": "warning",
                            "orphaned_filters": orphaned_count,
                            "message": f"{orphaned_count} orphaned filter definitions found"
                        }
                        print(f"⚠️  Data Integrity: {orphaned_count} orphaned filters")
                        if health_data["overall_status"] == "healthy":
                            health_data["overall_status"] = "degraded"
                
                except Exception as e:
                    health_data["checks"]["data_integrity"] = {"status": "error", "message": str(e)}
                    print(f"❌ Data Integrity: {str(e)}")
                
                # Check 5: Cache connectivity (if Redis is configured)
                try:
                    await self.filter_service.redis.ping()
                    health_data["checks"]["cache"] = {"status": "healthy", "message": "Redis connected"}
                    print("✅ Cache: Redis connected")
                except Exception as e:
                    health_data["checks"]["cache"] = {"status": "warning", "message": f"Redis issue: {str(e)}"}
                    print(f"⚠️  Cache: Redis issue - {str(e)}")
                    if health_data["overall_status"] == "healthy":
                        health_data["overall_status"] = "degraded"
            
            # Summary
            print("\n📊 Health Check Summary:")
            print(f"Overall Status: {health_data['overall_status'].upper()}")
            
            healthy_checks = sum(1 for check in health_data["checks"].values() if check["status"] == "healthy")
            total_checks = len(health_data["checks"])
            print(f"Checks Passed: {healthy_checks}/{total_checks}")
            
            return health_data
            
        except Exception as e:
            print(f"❌ Health check failed: {str(e)}")
            return {
                "timestamp": datetime.utcnow().isoformat(),
                "overall_status": "error",
                "error": str(e)
            }
    
    async def seed_sample_data(self) -> bool:
        """
        Seed the database with sample filter data.
        
        Returns:
            True if successful
        """
        try:
            print("🌱 Seeding sample filter data...")
            
            # Sample data for Instagram
            await self._seed_instagram_filters()
            
            # Sample data for YouTube  
            await self._seed_youtube_filters()
            
            # Sample location data
            await self._seed_location_data()
            
            print("✅ Sample data seeded successfully!")
            return True
            
        except Exception as e:
            print(f"❌ Error seeding data: {str(e)}")
            return False
    
    async def _seed_instagram_filters(self):
        """Seed Instagram-specific filters."""
        print("📸 Seeding Instagram filters...")
        
        # Demographics group for creators
        demo_group_id = await self.add_filter_group(
            name="Demography & Identity",
            channel="instagram",
            option_for="creator",
            sort_order=1
        )
        
        if demo_group_id:
            # Gender filter
            await self.add_filter_definition(
                group_id=demo_group_id,
                name="Gender",
                filter_type="radio_button",
                icon="gender-icon",
                options=[
                    {"label": "Male", "value": "male", "description": ""},
                    {"label": "Female", "value": "female", "description": ""},
                    {"label": "Other", "value": "other", "description": ""}
                ],
                db_field="gender",
                api_field="creator_gender"
            )
            
            # Age filter
            await self.add_filter_definition(
                group_id=demo_group_id,
                name="Age",
                filter_type="checkbox",
                icon="age-icon",
                has_minmax=True,
                options=[
                    {"label": "Teen", "value": "13-19", "description": "13-19"},
                    {"label": "Young Adult", "value": "20-35", "description": "20-35"},
                    {"label": "Adult", "value": "36-55", "description": "36-55"},
                    {"label": "Senior", "value": "56+", "description": "56+"}
                ],
                db_field="age_group",
                api_field="creator_age"
            )
        
        # Performance Metrics group
        perf_group_id = await self.add_filter_group(
            name="Performance Metrics",
            channel="instagram",
            option_for="creator",
            sort_order=2
        )
        
        if perf_group_id:
            # Follower Count filter
            await self.add_filter_definition(
                group_id=perf_group_id,
                name="Follower Count",
                filter_type="checkbox",
                icon="followers-icon",
                has_minmax=True,
                options=[
                    {"label": "Nano", "value": "1k-10k", "description": "1K-10K"},
                    {"label": "Micro", "value": "10k-50k", "description": "10K-50K"},
                    {"label": "Mid", "value": "50k-500k", "description": "50K-500K"},
                    {"label": "Macro", "value": "500k-1m", "description": "500K-1M"},
                    {"label": "Mega", "value": "1m+", "description": "1M+"}
                ],
                db_field="follower_count",
                api_field="follower_count"
            )
    
    async def _seed_youtube_filters(self):
        """Seed YouTube-specific filters."""
        print("📺 Seeding YouTube filters...")
        
        # Content & Niche group
        content_group_id = await self.add_filter_group(
            name="Content & Niche",
            channel="youtube",
            option_for="creator",
            sort_order=3
        )
        
        if content_group_id:
            # Category filter
            await self.add_filter_definition(
                group_id=content_group_id,
                name="Category",
                filter_type="checkbox",
                icon="category-icon",
                has_search_box=True,
                options=[
                    {"label": "Gaming", "value": "gaming", "description": "Gaming content"},
                    {"label": "Tech", "value": "technology", "description": "Technology reviews"},
                    {"label": "Lifestyle", "value": "lifestyle", "description": "Lifestyle content"},
                    {"label": "Education", "value": "education", "description": "Educational content"}
                ],
                db_field="content_category",
                api_field="category"
            )
    
    async def _seed_location_data(self):
        """Seed sample location hierarchy data."""
        print("📍 Seeding location data...")
        
        async with self.db.get_session() as session:
            # India
            india = LocationHierarchy(
                name="India",
                code="IN",
                level=0,
                tier="Country"
            )
            session.add(india)
            await session.flush()
            
            # Maharashtra
            maharashtra = LocationHierarchy(
                name="Maharashtra",
                code="MH",
                level=1,
                parent_id=india.id,
                tier="State"
            )
            session.add(maharashtra)
            await session.flush()
            
            # Mumbai
            mumbai = LocationHierarchy(
                name="Mumbai",
                code="MUM",
                level=2,
                parent_id=maharashtra.id,
                tier="Tier 1",
                population=12442373
            )
            session.add(mumbai)
            
            await session.commit()
            print("   📍 Added: India > Maharashtra > Mumbai")


async def main():
    """Main CLI entry point."""
    parser = argparse.ArgumentParser(
        description="CreatorVerse Filter System Management CLI",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s add-channel --name snapchat --display "Snapchat"
  %(prog)s add-filter-group --name "Brand Affinity" --channel instagram --option-for creator
  %(prog)s add-filter --group-id <uuid> --name "Brand Mentions" --type checkbox
  %(prog)s disable-channel --channel tiktok
  %(prog)s list-channels
  %(prog)s health-check
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Add channel command
    add_channel_parser = subparsers.add_parser('add-channel', help='Add a new channel/platform')
    add_channel_parser.add_argument('--name', required=True, help='Channel name (e.g., snapchat)')
    add_channel_parser.add_argument('--display', required=True, help='Display name (e.g., Snapchat)')
    
    # Add filter group command
    add_group_parser = subparsers.add_parser('add-filter-group', help='Add a new filter group')
    add_group_parser.add_argument('--name', required=True, help='Group name')
    add_group_parser.add_argument('--channel', required=True, help='Channel/platform')
    add_group_parser.add_argument('--option-for', required=True, choices=['creator', 'audience'], help='Target type')
    add_group_parser.add_argument('--sort-order', type=int, default=0, help='Display order')
    
    # Add filter command
    add_filter_parser = subparsers.add_parser('add-filter', help='Add a new filter definition')
    add_filter_parser.add_argument('--group-id', required=True, help='Parent group ID')
    add_filter_parser.add_argument('--name', required=True, help='Filter name')
    add_filter_parser.add_argument('--type', required=True, 
                                  choices=['radio_button', 'checkbox', 'multilevel_checkbox', 'enter_value'],
                                  help='Filter type')
    add_filter_parser.add_argument('--icon', help='Icon name/class')
    add_filter_parser.add_argument('--minmax', action='store_true', help='Supports min/max')
    add_filter_parser.add_argument('--enter-value', action='store_true', help='Allows text input')
    add_filter_parser.add_argument('--search-box', action='store_true', help='Has search functionality')
    add_filter_parser.add_argument('--placeholder', help='Placeholder text')
    add_filter_parser.add_argument('--options', help='JSON string of options')
    add_filter_parser.add_argument('--db-field', help='Database field mapping')
    add_filter_parser.add_argument('--api-field', help='API field mapping')
    add_filter_parser.add_argument('--sort-order', type=int, default=0, help='Display order')
    
    # Channel management commands
    enable_channel_parser = subparsers.add_parser('enable-channel', help='Enable a channel')
    enable_channel_parser.add_argument('--channel', required=True, help='Channel to enable')
    
    disable_channel_parser = subparsers.add_parser('disable-channel', help='Disable a channel')
    disable_channel_parser.add_argument('--channel', required=True, help='Channel to disable')
    
    # List commands
    subparsers.add_parser('list-channels', help='List all channels')
    
    list_groups_parser = subparsers.add_parser('list-groups', help='List filter groups')
    list_groups_parser.add_argument('--channel', help='Filter by channel')
    
    list_filters_parser = subparsers.add_parser('list-filters', help='List filter definitions')
    list_filters_parser.add_argument('--group-id', help='Filter by group ID')
    
    # Utility commands
    subparsers.add_parser('health-check', help='Perform system health check')
    subparsers.add_parser('seed-data', help='Seed sample filter data')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # Initialize filter manager
    filter_manager = FilterManager()
    
    try:
        if args.command == 'add-channel':
            await filter_manager.add_new_channel(args.name, args.display)
        
        elif args.command == 'add-filter-group':
            await filter_manager.add_filter_group(
                args.name, args.channel, getattr(args, 'option_for'), args.sort_order
            )
        
        elif args.command == 'add-filter':
            options = None
            if args.options:
                try:
                    options = json.loads(args.options)
                except json.JSONDecodeError:
                    print("❌ Invalid JSON format for options")
                    return
            
            await filter_manager.add_filter_definition(
                args.group_id, args.name, args.type, args.icon,
                args.minmax, getattr(args, 'enter_value'), getattr(args, 'search_box'),
                args.placeholder, options, args.db_field, args.api_field, args.sort_order
            )
        
        elif args.command == 'enable-channel':
            await filter_manager.toggle_channel_status(args.channel, True)
        
        elif args.command == 'disable-channel':
            await filter_manager.toggle_channel_status(args.channel, False)
        
        elif args.command == 'list-channels':
            await filter_manager.list_channels()
        
        elif args.command == 'list-groups':
            await filter_manager.list_filter_groups(args.channel)
        
        elif args.command == 'list-filters':
            await filter_manager.list_filters(args.group_id)
        
        elif args.command == 'health-check':
            await filter_manager.health_check()
        
        elif args.command == 'seed-data':
            await filter_manager.seed_sample_data()
    
    except Exception as e:
        print(f"❌ Command failed: {str(e)}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
