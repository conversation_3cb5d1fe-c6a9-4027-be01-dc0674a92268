#!/usr/bin/env python3
"""
Filter Catalog Schema Validation Script

This script validates that the filter_catalog schema is working correctly
after the fixes applied to resolve model-database mismatches.
"""

import asyncio
import sys
from typing import List, Dict, Any

from app.models.filter_models import (
    FilterGroup, FilterDefinition, LocationHierarchy, SavedFilterSet,
    FilterUsageLog, FilterTypeEnum, OptionForTypeEnum, PlatformTypeEnum
)
from app.core.config import get_database
from sqlalchemy import select, func, text


class FilterCatalogValidator:
    """Comprehensive validator for filter catalog schema"""
    
    def __init__(self):
        self.db = get_database()
        self.results = []
        self.errors = []
    
    def log_result(self, test_name: str, status: str, details: str = ""):
        """Log test result"""
        self.results.append({
            'test': test_name,
            'status': status,
            'details': details
        })
        status_icon = "✅" if status == "PASS" else "❌"
        print(f"{status_icon} {test_name}: {status}")
        if details:
            print(f"   {details}")
    
    def log_error(self, test_name: str, error: Exception):
        """Log test error"""
        self.errors.append({
            'test': test_name,
            'error': str(error),
            'type': type(error).__name__
        })
        print(f"❌ {test_name}: ERROR - {error}")
    
    async def test_basic_connectivity(self):
        """Test basic database connectivity"""
        try:
            async with self.db.get_db() as session:
                result = await session.execute(text("SELECT 1"))
                if result.scalar() == 1:
                    self.log_result("Database Connectivity", "PASS")
                else:
                    self.log_result("Database Connectivity", "FAIL", "Unexpected result")
        except Exception as e:
            self.log_error("Database Connectivity", e)
    
    async def test_filter_group_model(self):
        """Test FilterGroup model with all fields"""
        try:
            async with self.db.get_db() as session:
                # Test reading existing data
                result = await session.execute(select(FilterGroup).limit(1))
                groups = result.scalars().all()
                
                if groups:
                    group = groups[0]
                    # Test all fields are accessible
                    fields_to_test = [
                        'id', 'name', 'option_for', 'channel', 'sort_order',
                        'is_active', 'description', 'metadata_json', 'created_at', 'updated_at'
                    ]
                    
                    accessible_fields = []
                    for field in fields_to_test:
                        try:
                            value = getattr(group, field)
                            accessible_fields.append(field)
                        except AttributeError:
                            pass
                    
                    if len(accessible_fields) == len(fields_to_test):
                        self.log_result("FilterGroup Model Fields", "PASS", 
                                      f"All {len(fields_to_test)} fields accessible")
                    else:
                        missing = set(fields_to_test) - set(accessible_fields)
                        self.log_result("FilterGroup Model Fields", "FAIL", 
                                      f"Missing fields: {missing}")
                else:
                    self.log_result("FilterGroup Model Fields", "SKIP", "No data to test")
                    
        except Exception as e:
            self.log_error("FilterGroup Model Fields", e)
    
    async def test_filter_definition_model(self):
        """Test FilterDefinition model with all fields"""
        try:
            async with self.db.get_db() as session:
                # Test reading existing data
                result = await session.execute(select(FilterDefinition).limit(1))
                definitions = result.scalars().all()
                
                if definitions:
                    definition = definitions[0]
                    # Test all fields are accessible
                    fields_to_test = [
                        'id', 'group_id', 'name', 'icon', 'has_minmax', 'has_enter_value',
                        'has_search_box', 'placeholder', 'options', 'db_field', 'api_field',
                        'validation_rules', 'sort_order', 'is_active', 'created_at', 
                        'updated_at', 'filter_type'
                    ]
                    
                    accessible_fields = []
                    for field in fields_to_test:
                        try:
                            value = getattr(definition, field)
                            accessible_fields.append(field)
                        except AttributeError:
                            pass
                    
                    if len(accessible_fields) == len(fields_to_test):
                        self.log_result("FilterDefinition Model Fields", "PASS", 
                                      f"All {len(fields_to_test)} fields accessible")
                    else:
                        missing = set(fields_to_test) - set(accessible_fields)
                        self.log_result("FilterDefinition Model Fields", "FAIL", 
                                      f"Missing fields: {missing}")
                else:
                    self.log_result("FilterDefinition Model Fields", "SKIP", "No data to test")
                    
        except Exception as e:
            self.log_error("FilterDefinition Model Fields", e)
    
    async def test_create_operations(self):
        """Test creating new records"""
        try:
            async with self.db.get_db() as session:
                # Test creating FilterGroup
                test_group = FilterGroup(
                    name="Validation Test Group",
                    option_for=OptionForTypeEnum.creator,
                    channel=PlatformTypeEnum.instagram,
                    description="Test description for validation",
                    metadata_json={"test": True, "validation": "script"},
                    sort_order=999,
                    is_active=True
                )
                session.add(test_group)
                await session.flush()
                
                # Test creating FilterDefinition
                test_definition = FilterDefinition(
                    group_id=test_group.id,
                    name="Validation Test Filter",
                    filter_type=FilterTypeEnum.checkbox,
                    icon="test-icon",
                    has_minmax=False,
                    has_enter_value=False,
                    has_search_box=True,
                    placeholder="Test placeholder",
                    options=[{"label": "Test Option", "value": "test", "description": "Test"}],
                    db_field="test_field",
                    api_field="test_api_field",
                    validation_rules={"required": True, "min_length": 1},
                    sort_order=1,
                    is_active=True
                )
                session.add(test_definition)
                await session.flush()
                
                # Verify the records were created
                if test_group.id and test_definition.id:
                    self.log_result("Create Operations", "PASS", 
                                  f"Created group {test_group.id} and definition {test_definition.id}")
                else:
                    self.log_result("Create Operations", "FAIL", "Records not created properly")
                
                # Clean up - rollback
                await session.rollback()
                
        except Exception as e:
            self.log_error("Create Operations", e)
    
    async def test_relationships(self):
        """Test model relationships"""
        try:
            async with self.db.get_db() as session:
                # Test FilterGroup -> FilterDefinition relationship using explicit joins
                from sqlalchemy.orm import selectinload

                result = await session.execute(
                    select(FilterGroup)
                    .options(selectinload(FilterGroup.filter_definitions))
                    .limit(1)
                )
                groups = result.scalars().all()

                if groups:
                    group = groups[0]
                    definitions = group.filter_definitions

                    if definitions and len(definitions) > 0:
                        # Test reverse relationship
                        definition = definitions[0]
                        # Load the group explicitly to avoid lazy loading issues
                        result = await session.execute(
                            select(FilterDefinition)
                            .options(selectinload(FilterDefinition.group))
                            .where(FilterDefinition.id == definition.id)
                        )
                        loaded_definition = result.scalar_one()

                        if loaded_definition.group.id == group.id:
                            self.log_result("Model Relationships", "PASS",
                                          f"Bidirectional relationship working")
                        else:
                            self.log_result("Model Relationships", "FAIL",
                                          "Reverse relationship not working")
                    else:
                        self.log_result("Model Relationships", "SKIP",
                                      "No definitions found for testing")
                else:
                    self.log_result("Model Relationships", "SKIP",
                                  "No groups found for testing")

        except Exception as e:
            self.log_error("Model Relationships", e)
    
    async def test_enum_validations(self):
        """Test enum field validations"""
        try:
            async with self.db.get_db() as session:
                # Test valid enum values
                test_group = FilterGroup(
                    name="Enum Test Group",
                    option_for=OptionForTypeEnum.audience,  # Valid enum
                    channel=PlatformTypeEnum.youtube,       # Valid enum
                )
                session.add(test_group)
                await session.flush()
                
                test_definition = FilterDefinition(
                    group_id=test_group.id,
                    name="Enum Test Filter",
                    filter_type=FilterTypeEnum.range_slider  # Valid enum
                )
                session.add(test_definition)
                await session.flush()
                
                self.log_result("Enum Validations", "PASS", "Valid enum values accepted")
                
                # Clean up
                await session.rollback()
                
        except Exception as e:
            self.log_error("Enum Validations", e)
    
    async def test_json_fields(self):
        """Test JSONB field operations"""
        try:
            async with self.db.get_db() as session:
                # Test complex JSON data
                complex_metadata = {
                    "ui_config": {
                        "theme": "dark",
                        "layout": "grid"
                    },
                    "permissions": ["read", "write"],
                    "settings": {
                        "auto_refresh": True,
                        "cache_duration": 3600
                    }
                }
                
                complex_options = [
                    {
                        "label": "Option 1",
                        "value": "opt1",
                        "description": "First option",
                        "metadata": {"priority": 1}
                    },
                    {
                        "label": "Option 2", 
                        "value": "opt2",
                        "description": "Second option",
                        "metadata": {"priority": 2}
                    }
                ]
                
                complex_validation = {
                    "required": True,
                    "type": "array",
                    "min_items": 1,
                    "max_items": 5,
                    "items": {
                        "type": "string",
                        "pattern": "^[a-zA-Z0-9_]+$"
                    }
                }
                
                test_group = FilterGroup(
                    name="JSON Test Group",
                    option_for=OptionForTypeEnum.creator,
                    channel=PlatformTypeEnum.tiktok,
                    metadata_json=complex_metadata
                )
                session.add(test_group)
                await session.flush()
                
                test_definition = FilterDefinition(
                    group_id=test_group.id,
                    name="JSON Test Filter",
                    filter_type=FilterTypeEnum.multilevel_checkbox,
                    options=complex_options,
                    validation_rules=complex_validation
                )
                session.add(test_definition)
                await session.flush()
                
                # Verify JSON data was stored correctly
                if (test_group.metadata_json == complex_metadata and
                    test_definition.options == complex_options and
                    test_definition.validation_rules == complex_validation):
                    self.log_result("JSON Fields", "PASS", "Complex JSON data stored and retrieved correctly")
                else:
                    self.log_result("JSON Fields", "FAIL", "JSON data mismatch")
                
                # Clean up
                await session.rollback()
                
        except Exception as e:
            self.log_error("JSON Fields", e)
    
    async def run_all_tests(self):
        """Run all validation tests"""
        print("🧪 Starting Filter Catalog Schema Validation")
        print("=" * 50)
        
        tests = [
            self.test_basic_connectivity,
            self.test_filter_group_model,
            self.test_filter_definition_model,
            self.test_create_operations,
            self.test_relationships,
            self.test_enum_validations,
            self.test_json_fields
        ]
        
        for test in tests:
            await test()
            print()  # Add spacing between tests
        
        # Summary
        print("=" * 50)
        print("📊 VALIDATION SUMMARY")
        print("=" * 50)
        
        passed = len([r for r in self.results if r['status'] == 'PASS'])
        failed = len([r for r in self.results if r['status'] == 'FAIL'])
        skipped = len([r for r in self.results if r['status'] == 'SKIP'])
        errors = len(self.errors)
        
        print(f"✅ Passed: {passed}")
        print(f"❌ Failed: {failed}")
        print(f"⏭️  Skipped: {skipped}")
        print(f"🚨 Errors: {errors}")
        
        if failed == 0 and errors == 0:
            print("\n🎉 ALL TESTS PASSED! Filter catalog schema is working correctly.")
            return True
        else:
            print(f"\n⚠️  {failed + errors} issues found. Please review the results above.")
            return False


async def main():
    """Main validation function"""
    validator = FilterCatalogValidator()
    success = await validator.run_all_tests()
    
    if success:
        sys.exit(0)
    else:
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
