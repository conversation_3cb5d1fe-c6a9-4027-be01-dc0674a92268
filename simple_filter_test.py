#!/usr/bin/env python3
"""
Simplified Filter System Test Application
Runs only the filter system without complex dependencies
"""

import asyncio
import sys
import os
import traceback
from contextlib import asynccontextmanager
from datetime import datetime
from typing import Optional, List, Dict, Any
from uuid import UUID
import json

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from fastapi import FastAPI, HTTPException, Depends, Query, Path
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn

# Simple mock dependencies to avoid import errors
class MockRedisClient:
    async def get(self, key: str): return None
    async def setex(self, key: str, ttl: int, value: str): pass
    async def delete(self, *keys): pass
    async def keys(self, pattern: str): return []
    async def ping(self): return True

class MockDatabase:
    async def get_session(self): 
        return MockSession()

class MockSession:
    async def execute(self, query): 
        return MockResult()
    async def commit(self): pass
    async def rollback(self): pass
    def add(self, obj): pass
    async def __aenter__(self): return self
    async def __aexit__(self, exc_type, exc_val, exc_tb): pass

class MockResult:
    def scalars(self): return MockScalars()
    def scalar_one_or_none(self): return None
    def fetchval(self, sql): return 0
    def all(self): return []

class MockScalars:
    def all(self): return []
    def first(self): return None

# Mock the complex imports and dependencies
sys.modules['app.models.filter_models'] = type('MockModule', (), {
    'PlatformTypeEnum': type('PlatformTypeEnum', (), {
        'instagram': 'instagram',
        'youtube': 'youtube', 
        'tiktok': 'tiktok'
    })(),
    'OptionForTypeEnum': type('OptionForTypeEnum', (), {
        'creator': 'creator',
        'audience': 'audience'
    })(),
    'FilterGroup': object,
    'FilterDefinition': object,
    'LocationHierarchy': object,
    'SavedFilterSet': object,
    'FilterUsageLog': object
})()

sys.modules['app.schemas.filter_schemas'] = type('MockModule', (), {
    'FilterGroupSchema': dict,
    'FilterDefinitionSchema': dict,
    'LocationHierarchySchema': dict,
    'SavedFilterSetSchema': dict,
    'FilterMetadataSchema': dict,
    'GetFiltersRequest': dict,
    'SaveFilterSetRequest': dict,
    'ApplyFiltersRequest': dict,
    'FilterUsageStatsSchema': dict,
    'FilteredCreatorsResponse': dict
})()

sys.modules['app.services.filter_service'] = type('MockModule', (), {
    'FilterService': lambda: MockFilterService()
})()

sys.modules['app.core.exceptions'] = type('MockModule', (), {
    'CreatorVerseError': Exception
})()

sys.modules['app.api.dependencies'] = type('MockModule', (), {
    'get_db_session': lambda: MockSession(),
    'get_current_user_optional': lambda: None
})()

class MockFilterService:
    async def get_filter_groups(self, channel, option_for, include_inactive=False):
        # Return sample filter data based on database
        if channel == 'instagram' and option_for == 'creator':
            return [
                {
                    'id': '550e8400-e29b-41d4-a716-446655440000',
                    'optionName': 'Demography & Identity',
                    'optionFor': 'creator',
                    'channel': 'instagram',
                    'sort_order': 1,
                    'filters': [
                        {
                            'id': '550e8400-e29b-41d4-a716-446655440001',
                            'name': 'Gender',
                            'type': 'radio_button',
                            'icon': 'gender-icon',
                            'minmax': False,
                            'enterValue': False,
                            'searchBox': False,
                            'placeholder': 'Select Gender',
                            'options': [
                                {'label': 'Male', 'value': 'male', 'description': ''},
                                {'label': 'Female', 'value': 'female', 'description': ''},
                                {'label': 'Other', 'value': 'other', 'description': ''}
                            ],
                            'sort_order': 1
                        },
                        {
                            'id': '550e8400-e29b-41d4-a716-446655440002',
                            'name': 'Age',
                            'type': 'checkbox',
                            'icon': 'age-icon',
                            'minmax': True,
                            'enterValue': False,
                            'searchBox': False,
                            'placeholder': 'Select Age',
                            'options': [
                                {'label': 'Teen', 'value': '13-19', 'description': '13-19'},
                                {'label': 'Young Adult', 'value': '20-35', 'description': '20-35'},
                                {'label': 'Adult', 'value': '36-55', 'description': '36-55'},
                                {'label': 'Senior', 'value': '56+', 'description': '56+'}
                            ],
                            'sort_order': 2
                        }
                    ]
                },
                {
                    'id': '550e8400-e29b-41d4-a716-446655440003',
                    'optionName': 'Performance Metrics',
                    'optionFor': 'creator',
                    'channel': 'instagram',
                    'sort_order': 2,
                    'filters': [
                        {
                            'id': '550e8400-e29b-41d4-a716-446655440004',
                            'name': 'Follower Count',
                            'type': 'checkbox',
                            'icon': 'followers-icon',
                            'minmax': True,
                            'enterValue': False,
                            'searchBox': False,
                            'placeholder': 'Select Follower Range',
                            'options': [
                                {'label': 'Nano', 'value': '1k-10k', 'description': '1K-10K'},
                                {'label': 'Micro', 'value': '10k-50k', 'description': '10K-50K'},
                                {'label': 'Mid', 'value': '50k-500k', 'description': '50K-500K'},
                                {'label': 'Macro', 'value': '500k-1m', 'description': '500K-1M'},
                                {'label': 'Mega', 'value': '1m+', 'description': '1M+'}
                            ],
                            'sort_order': 1
                        }
                    ]
                }
            ]
        return []
    
    async def get_location_hierarchy(self, parent_id=None, tier=None, level=None):
        return [
            {'id': '1', 'name': 'Mumbai', 'code': 'MH-MM', 'level': 2, 'tier': 'Tier 1', 'parent_id': None},
            {'id': '2', 'name': 'Delhi', 'code': 'DL-DL', 'level': 2, 'tier': 'Tier 1', 'parent_id': None},
            {'id': '3', 'name': 'Bangalore', 'code': 'KA-BG', 'level': 2, 'tier': 'Tier 1', 'parent_id': None}
        ]
    
    async def get_filter_metadata(self):
        return {
            'total_groups': 18,
            'total_filters': 15,
            'active_groups': 18,
            'active_filters': 15,
            'platform_breakdown': {'instagram': 6, 'youtube': 6, 'tiktok': 6},
            'last_updated': datetime.utcnow()
        }
    
    async def toggle_filter_status(self, filter_id, is_active):
        return True
    
    async def toggle_group_status(self, group_id, is_active):
        return True

# Create the simplified app
@asynccontextmanager
async def lifespan(app: FastAPI):
    """Simple lifespan for testing"""
    print("🚀 Starting Filter System Test Server...")
    yield
    print("🛑 Stopping Filter System Test Server...")

app = FastAPI(
    title="CreatorVerse Filter System Test",
    description="Simplified test server for filter system endpoints",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mock platform and option enums
class PlatformType:
    instagram = "instagram"
    youtube = "youtube"
    tiktok = "tiktok"

class OptionType:
    creator = "creator"
    audience = "audience"

# Initialize mock service
filter_service = MockFilterService()

# Filter endpoints
@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "CreatorVerse Filter System Test Server",
        "status": "running",
        "endpoints": {
            "filters": "/api/v1/filters/",
            "health": "/api/v1/filters/health",
            "sample_data": "/api/v1/filters/test/sample-data",
            "docs": "/docs"
        }
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "filter-system-test"}

@app.get("/api/v1/filters/")
async def get_filters(
    channel: str = Query(..., description="Platform/channel"),
    option_for: str = Query(..., description="Creator or audience filters"),
    include_inactive: bool = Query(False, description="Include inactive filters")
):
    """Get available filters for a specific platform and target"""
    try:
        filter_groups = await filter_service.get_filter_groups(
            channel=channel,
            option_for=option_for,
            include_inactive=include_inactive
        )
        return filter_groups
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to retrieve filters: {str(e)}")

@app.get("/api/v1/filters/groups")
async def get_filter_groups(
    channel: Optional[str] = Query(None, description="Filter by platform"),
    option_for: Optional[str] = Query(None, description="Filter by target type"),
    include_inactive: bool = Query(False, description="Include inactive groups")
):
    """Get filter groups"""
    try:
        all_groups = []
        channels = [channel] if channel else ['instagram', 'youtube', 'tiktok']
        options = [option_for] if option_for else ['creator', 'audience']
        
        for ch in channels:
            for opt in options:
                groups = await filter_service.get_filter_groups(
                    channel=ch,
                    option_for=opt,
                    include_inactive=include_inactive
                )
                all_groups.extend(groups)
        
        return all_groups
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to retrieve filter groups: {str(e)}")

@app.get("/api/v1/filters/locations")
async def get_locations(
    parent_id: Optional[str] = Query(None, description="Parent location ID"),
    tier: Optional[str] = Query(None, description="Tier classification"),
    level: Optional[int] = Query(None, description="Hierarchy level")
):
    """Get location hierarchy"""
    try:
        return await filter_service.get_location_hierarchy(
            parent_id=parent_id,
            tier=tier,
            level=level
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to retrieve locations: {str(e)}")

@app.put("/api/v1/filters/filters/{filter_id}/status")
async def toggle_filter_status(
    filter_id: str,
    is_active: bool = Query(..., description="New active status")
):
    """Toggle filter status"""
    try:
        success = await filter_service.toggle_filter_status(filter_id, is_active)
        return {
            "success": success,
            "message": f"Filter {'enabled' if is_active else 'disabled'} successfully",
            "filter_id": filter_id,
            "is_active": is_active
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to toggle filter status: {str(e)}")

@app.put("/api/v1/filters/groups/{group_id}/status")
async def toggle_group_status(
    group_id: str,
    is_active: bool = Query(..., description="New active status")
):
    """Toggle group status"""
    try:
        success = await filter_service.toggle_group_status(group_id, is_active)
        return {
            "success": success,
            "message": f"Filter group {'enabled' if is_active else 'disabled'} successfully",
            "group_id": group_id,
            "is_active": is_active
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to toggle group status: {str(e)}")

@app.get("/api/v1/filters/health")
async def filter_health_check():
    """Filter system health check"""
    try:
        metadata = await filter_service.get_filter_metadata()
        return {
            "status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "components": {
                "metadata": {
                    "status": "healthy",
                    "total_groups": metadata.get('total_groups', 0),
                    "active_groups": metadata.get('active_groups', 0),
                    "total_filters": metadata.get('total_filters', 0),
                    "active_filters": metadata.get('active_filters', 0)
                },
                "cache": {"status": "healthy"},
                "database": {"status": "healthy"}
            }
        }
    except Exception as e:
        return {
            "status": "error",
            "timestamp": datetime.utcnow().isoformat(),
            "error": str(e)
        }

@app.get("/api/v1/filters/test/sample-data")
async def generate_sample_filter_response(
    channel: str = Query("instagram", description="Platform"),
    option_for: str = Query("creator", description="Target type")
):
    """Generate sample filter response"""
    try:
        filters = await filter_service.get_filter_groups(channel, option_for)
        return {
            "source": "mock_service",
            "channel": channel,
            "option_for": option_for,
            "filter_groups": filters
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to generate sample data: {str(e)}")

# Test runner
async def run_comprehensive_tests():
    """Run comprehensive tests on the filter system"""
    import httpx
    
    print("\n🧪 Running Comprehensive Filter System Tests...")
    print("=" * 60)
    
    base_url = "http://localhost:8001"
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        tests = [
            ("Root Endpoint", "GET", "/"),
            ("Health Check", "GET", "/health"),
            ("Filter System Health", "GET", "/api/v1/filters/health"),
            ("Instagram Creator Filters", "GET", "/api/v1/filters/?channel=instagram&option_for=creator"),
            ("Instagram Audience Filters", "GET", "/api/v1/filters/?channel=instagram&option_for=audience"),
            ("YouTube Creator Filters", "GET", "/api/v1/filters/?channel=youtube&option_for=creator"),
            ("TikTok Creator Filters", "GET", "/api/v1/filters/?channel=tiktok&option_for=creator"),
            ("All Filter Groups", "GET", "/api/v1/filters/groups"),
            ("Location Hierarchy", "GET", "/api/v1/filters/locations"),
            ("Sample Data", "GET", "/api/v1/filters/test/sample-data"),
        ]
        
        results = {"passed": 0, "failed": 0, "details": []}
        
        for test_name, method, endpoint in tests:
            try:
                print(f"\n📋 Testing: {test_name}")
                print(f"   {method} {endpoint}")
                
                response = await client.request(method, f"{base_url}{endpoint}")
                response.raise_for_status()
                data = response.json()
                
                print(f"   ✅ Status: {response.status_code}")
                
                # Specific validations
                if "filters" in endpoint and "channel=" in endpoint:
                    if isinstance(data, list) and len(data) > 0:
                        print(f"   📊 Found {len(data)} filter groups")
                        for group in data:
                            if 'filters' in group:
                                print(f"      • {group.get('optionName', 'Unknown')}: {len(group['filters'])} filters")
                    else:
                        print(f"   ⚠️  No filter groups returned")
                
                elif endpoint == "/api/v1/filters/health":
                    status = data.get('status', 'unknown')
                    print(f"   💊 Health Status: {status}")
                    if 'components' in data:
                        for component, details in data['components'].items():
                            print(f"      • {component}: {details.get('status', 'unknown')}")
                
                results["passed"] += 1
                results["details"].append({"test": test_name, "status": "✅ PASSED", "data_size": len(str(data))})
                
            except Exception as e:
                print(f"   ❌ Failed: {str(e)}")
                results["failed"] += 1
                results["details"].append({"test": test_name, "status": "❌ FAILED", "error": str(e)})
        
        # Test admin functions
        try:
            print(f"\n📋 Testing: Admin Filter Toggle")
            response = await client.put(f"{base_url}/api/v1/filters/filters/test-id/status?is_active=false")
            if response.status_code == 200:
                print(f"   ✅ Filter toggle working")
                results["passed"] += 1
            else:
                print(f"   ⚠️  Filter toggle returned {response.status_code}")
                results["failed"] += 1
        except Exception as e:
            print(f"   ❌ Filter toggle failed: {str(e)}")
            results["failed"] += 1
        
        # Summary
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print("=" * 60)
        
        total = results["passed"] + results["failed"]
        print(f"📈 Total Tests: {total}")
        print(f"✅ Passed: {results['passed']}")
        print(f"❌ Failed: {results['failed']}")
        print(f"🎯 Success Rate: {(results['passed']/total*100):.1f}%" if total > 0 else "0%")
        
        print(f"\n📋 Detailed Results:")
        for detail in results["details"]:
            print(f"   {detail['status']} {detail['test']}")
        
        if results['passed'] == total:
            print(f"\n🎉 All tests passed! Filter system is working correctly.")
        else:
            print(f"\n⚠️  {results['failed']} test(s) failed. Check the errors above.")
        
        return results

# Start server with testing
async def start_server_and_test():
    """Start server in background and run tests"""
    import subprocess
    import time
    
    # Start server in background
    print("🚀 Starting test server...")
    
    # Use uvicorn to run this file as a module
    try:
        # Start server
        server_process = None
        config = uvicorn.Config(
            "simple_filter_test:app",
            host="0.0.0.0",
            port=8001,
            log_level="info"
        )
        server = uvicorn.Server(config)
        
        # Run tests after a short delay
        await asyncio.sleep(2)
        results = await run_comprehensive_tests()
        
        return results
        
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        return None

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "test":
        # Run tests against running server
        asyncio.run(run_comprehensive_tests())
    else:
        # Start server
        print("🚀 Starting CreatorVerse Filter System Test Server...")
        print("📊 Server will be available at: http://localhost:8001")
        print("📖 API Documentation: http://localhost:8001/docs")
        print("🧪 To run tests: python simple_filter_test.py test")
        
        uvicorn.run(
            "simple_filter_test:app",
            host="0.0.0.0",
            port=8001,
            reload=False,
            log_level="info"
        )
