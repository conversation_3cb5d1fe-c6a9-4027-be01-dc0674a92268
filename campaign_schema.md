-- DROP SCHEMA campaigns;

CREATE SCHEMA campaigns AUTHORIZAT<PERSON> postgres;
-- campaigns.master_channels definition

-- Drop table

-- DROP TABLE campaigns.master_channels;

CREATE TABLE campaigns.master_channels (
	id uuid DEFAULT gen_random_uuid() NOT NULL,
	"name" varchar(64) NOT NULL,
	metadata jsonb NULL,
	is_active bool DEFAULT true NULL,
	created_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
	updated_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
	CONSTRAINT master_channels_name_key UNIQUE (name),
	CONSTRAINT master_channels_pkey PRIMARY KEY (id)
);


-- campaigns.master_currencies definition

-- Drop table

-- DROP TABLE campaigns.master_currencies;

CREATE TABLE campaigns.master_currencies (
	id uuid DEFAULT gen_random_uuid() NOT NULL,
	code bpchar(3) NOT NULL,
	"name" varchar(64) NOT NULL,
	"type" varchar(16) NULL,
	is_active bool DEFAULT true NULL,
	created_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
	updated_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
	CONSTRAINT master_currencies_code_key UNIQUE (code),
	CONSTRAINT master_currencies_pkey PRIMARY KEY (id)
);


-- campaigns.master_inner_channels definition

-- Drop table

-- DROP TABLE campaigns.master_inner_channels;

CREATE TABLE campaigns.master_inner_channels (
	id uuid DEFAULT gen_random_uuid() NOT NULL,
	"name" varchar(64) NOT NULL,
	description text NULL,
	is_active bool DEFAULT true NULL,
	created_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
	updated_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
	CONSTRAINT master_inner_channels_name_key UNIQUE (name),
	CONSTRAINT master_inner_channels_pkey PRIMARY KEY (id)
);


-- campaigns.master_payment_types definition

-- Drop table

-- DROP TABLE campaigns.master_payment_types;

CREATE TABLE campaigns.master_payment_types (
	id uuid DEFAULT gen_random_uuid() NOT NULL,
	code varchar(32) NOT NULL,
	"name" varchar(64) NOT NULL,
	description varchar(128) NULL,
	is_active bool DEFAULT true NULL,
	created_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
	updated_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
	CONSTRAINT master_payment_types_code_key UNIQUE (code),
	CONSTRAINT master_payment_types_pkey PRIMARY KEY (id)
);


-- campaigns.campaign_channel_inner_requirements definition

-- Drop table

-- DROP TABLE campaigns.campaign_channel_inner_requirements;

CREATE TABLE campaigns.campaign_channel_inner_requirements (
	id uuid DEFAULT gen_random_uuid() NOT NULL,
	campaign_id uuid NOT NULL,
	channel_id uuid NOT NULL,
	inner_channel_id uuid NOT NULL,
	required_count int4 NOT NULL,
	negotiable bool DEFAULT false NULL,
	min_count int4 NULL,
	max_count int4 NULL,
	metadata jsonb NULL,
	created_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
	updated_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
	CONSTRAINT campaign_channel_inner_requirements_pkey PRIMARY KEY (id),
	CONSTRAINT chk_neg_range CHECK ((((negotiable = false) AND (min_count IS NULL) AND (max_count IS NULL)) OR ((negotiable = true) AND (min_count > 0) AND (min_count <= required_count) AND (required_count <= max_count)))),
	CONSTRAINT uq_req UNIQUE (campaign_id, channel_id, inner_channel_id)
);
CREATE INDEX idx_campaign_channel_req_campaign_id ON campaigns.campaign_channel_inner_requirements USING btree (campaign_id);


-- campaigns.campaign_influencer_deliverables definition

-- Drop table

-- DROP TABLE campaigns.campaign_influencer_deliverables;

CREATE TABLE campaigns.campaign_influencer_deliverables (
	id uuid DEFAULT gen_random_uuid() NOT NULL,
	campaign_influencer_id uuid NOT NULL,
	channel_id uuid NOT NULL,
	inner_channel_id uuid NOT NULL,
	content_url text NOT NULL,
	posted_at timestamptz NULL,
	scheduled_at timestamptz NULL,
	is_approved bool DEFAULT false NULL,
	notes text NULL,
	is_delete bool DEFAULT false NULL,
	created_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
	updated_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
	CONSTRAINT campaign_influencer_deliverables_pkey PRIMARY KEY (id)
);
CREATE INDEX idx_campaign_deliverables_campaign_inf_id ON campaigns.campaign_influencer_deliverables USING btree (campaign_influencer_id);
CREATE INDEX idx_campaign_deliverables_posted_at ON campaigns.campaign_influencer_deliverables USING btree (posted_at);
CREATE INDEX idx_campaign_deliverables_scheduled_at ON campaigns.campaign_influencer_deliverables USING btree (scheduled_at);


-- campaigns.campaign_influencer_inner_requirements definition

-- Drop table

-- DROP TABLE campaigns.campaign_influencer_inner_requirements;

CREATE TABLE campaigns.campaign_influencer_inner_requirements (
	id uuid DEFAULT gen_random_uuid() NOT NULL,
	campaign_influencer_id uuid NOT NULL,
	channel_id uuid NOT NULL,
	inner_channel_id uuid NOT NULL,
	initial_required_count int4 NULL,
	initial_min_count int4 NULL,
	initial_max_count int4 NULL,
	negotiated_count int4 NULL,
	is_finalized bool DEFAULT false NULL,
	notes text NULL,
	created_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
	updated_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
	CONSTRAINT campaign_influencer_inner_requirements_pkey PRIMARY KEY (id),
	CONSTRAINT chk_neg_range_inf CHECK (((negotiated_count >= initial_min_count) AND (negotiated_count <= initial_max_count)))
);
CREATE INDEX idx_campaign_inf_req_campaign_inf_id ON campaigns.campaign_influencer_inner_requirements USING btree (campaign_influencer_id);


-- campaigns.campaign_influencers definition

-- Drop table

-- DROP TABLE campaigns.campaign_influencers;

CREATE TABLE campaigns.campaign_influencers (
	id uuid DEFAULT gen_random_uuid() NOT NULL,
	campaign_id uuid NOT NULL,
	influencer_user_id uuid NULL,
	created_by uuid NOT NULL,
	status varchar(20) DEFAULT 'INVITED'::character varying NULL,
	invitation_sent_at timestamptz NULL,
	response_deadline timestamptz NULL,
	applied_at timestamptz NULL,
	assigned_at timestamptz NULL,
	negotiation_complete timestamptz NULL,
	notes text NULL,
	is_delete bool DEFAULT false NULL,
	created_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
	updated_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
	CONSTRAINT campaign_influencers_pkey PRIMARY KEY (id),
	CONSTRAINT campaign_influencers_status_check CHECK (((status)::text = ANY ((ARRAY['INVITED'::character varying, 'APPLIED'::character varying, 'ACCEPTED'::character varying, 'DECLINED'::character varying, 'REMOVED'::character varying, 'COMPLETED'::character varying])::text[]))),
	CONSTRAINT uq_participation UNIQUE (campaign_id, influencer_user_id)
);
CREATE INDEX idx_campaign_influencers_campaign_id ON campaigns.campaign_influencers USING btree (campaign_id);
CREATE INDEX idx_campaign_influencers_status ON campaigns.campaign_influencers USING btree (status);


-- campaigns.campaigns definition

-- Drop table

-- DROP TABLE campaigns.campaigns;

CREATE TABLE campaigns.campaigns (
	id uuid DEFAULT gen_random_uuid() NOT NULL,
	organization_id uuid NOT NULL,
	created_by uuid NOT NULL,
	"name" text NOT NULL,
	description text NULL,
	status text DEFAULT 'DRAFT'::text NULL,
	application_deadline date NULL,
	start_date date NULL,
	end_date date NULL,
	budget numeric NULL,
	currency_id uuid NULL,
	target_influencer_count int4 NULL,
	payment_type_id uuid NULL,
	hashtags _text NULL,
	emotions _text NULL,
	is_delete bool DEFAULT false NULL,
	created_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
	updated_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
	CONSTRAINT campaigns_pkey PRIMARY KEY (id),
	CONSTRAINT campaigns_status_check CHECK ((status = ANY (ARRAY['DRAFT'::text, 'ACTIVE'::text, 'PAUSED'::text, 'COMPLETED'::text, 'CANCELLED'::text])))
);


-- campaigns.campaign_channel_inner_requirements foreign keys

ALTER TABLE campaigns.campaign_channel_inner_requirements ADD CONSTRAINT campaign_channel_inner_requirements_campaign_id_fkey FOREIGN KEY (campaign_id) REFERENCES campaigns.campaigns(id);


-- campaigns.campaign_influencer_deliverables foreign keys

ALTER TABLE campaigns.campaign_influencer_deliverables ADD CONSTRAINT campaign_influencer_deliverables_campaign_influencer_id_fkey FOREIGN KEY (campaign_influencer_id) REFERENCES campaigns.campaign_influencers(id);


-- campaigns.campaign_influencer_inner_requirements foreign keys

ALTER TABLE campaigns.campaign_influencer_inner_requirements ADD CONSTRAINT campaign_influencer_inner_requireme_campaign_influencer_id_fkey FOREIGN KEY (campaign_influencer_id) REFERENCES campaigns.campaign_influencers(id);


-- campaigns.campaign_influencers foreign keys

ALTER TABLE campaigns.campaign_influencers ADD CONSTRAINT campaign_influencers_campaign_id_fkey FOREIGN KEY (campaign_id) REFERENCES campaigns.campaigns(id);


-- campaigns.campaigns foreign keys

ALTER TABLE campaigns.campaigns ADD CONSTRAINT campaigns_created_by_fkey FOREIGN KEY (created_by) REFERENCES users.users(id);
ALTER TABLE campaigns.campaigns ADD CONSTRAINT campaigns_currency_id_fkey FOREIGN KEY (currency_id) REFERENCES campaigns.master_currencies(id);
ALTER TABLE campaigns.campaigns ADD CONSTRAINT campaigns_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES users.organizations(id);
ALTER TABLE campaigns.campaigns ADD CONSTRAINT campaigns_payment_type_id_fkey FOREIGN KEY (payment_type_id) REFERENCES campaigns.master_payment_types(id);