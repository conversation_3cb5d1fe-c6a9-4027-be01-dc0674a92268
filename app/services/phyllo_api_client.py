"""
Proper Phyllo API Client implementing the three required endpoints:
1. Quick Search API
2. Advanced Search API  
3. Profile Analytics API
"""
import json
import httpx
import hashlib
from datetime import datetime, timed<PERSON><PERSON>
from typing import List, Dict, Any, Optional, Tuple
from uuid import UUID

from app.core.config import APP_CONFIG, get_discovery_redis
from app.core.exceptions import CreatorVerseError, ExternalAPIError
from app.schemas.filter_schemas import DiscoveryFilters, PlatformTypeEnum
from app.models.enhanced_profile_models import EnhancedProfile, ContentItem, ProfileAnalyticsCache
from app.core_helper.async_logger import with_trace_id


class PhylloAPIClient:
    """
    Production-ready Phyllo API client implementing all three required endpoints
    """
    
    def __init__(self):
        self.logger = APP_CONFIG.logger or APP_CONFIG.initialize_logger()
        self.redis = get_discovery_redis()
        self.base_url = APP_CONFIG.phyllo_base_url
        self.client_id = APP_CONFIG.phyllo_client_id
        self.client_secret = APP_CONFIG.phyllo_client_secret
        self._access_token = None
        self._token_expires_at = None
        
        # API endpoints
        self.endpoints = {
            'quick_search': '/v1/social/creator-profile/quick-search',
            'advanced_search': '/v1/social/creator-profile/search',
            'profile_analytics': '/v1/social/creator-profile/analytics',
            'auth': '/v1/auth/token'
        }
        
        # Cache TTL settings
        self.cache_ttl = {
            'quick_search': 300,  # 5 minutes
            'advanced_search': 1800,  # 30 minutes
            'profile_analytics': 3600  # 1 hour
        }
    
    @with_trace_id
    async def quick_search(
        self,
        query: str,
        platform: PlatformTypeEnum,
        limit: int = 10,
        use_cache: bool = True
    ) -> List[Dict[str, Any]]:
        """
        Phyllo Quick Search API - Fast discovery with basic filters
        https://docs.getphyllo.com/docs/api-reference/api/ref/operations/create-a-v-1-social-creator-profile-quick-search
        """
        try:
            # Generate cache key
            cache_key = self._generate_cache_key('quick_search', {
                'query': query,
                'platform': platform.value,
                'limit': limit
            })
            
            # Try cache first
            if use_cache:
                cached_result = await self._get_from_cache(cache_key)
                if cached_result:
                    self.logger.info(f"Quick search cache hit for query: {query}")
                    return cached_result
            
            # Prepare request payload
            payload = {
                "query": query,
                "platform": platform.value,
                "limit": limit,
                "include_metrics": True,
                "include_demographics": True
            }
            
            # Make API request
            response_data = await self._make_api_request(
                endpoint=self.endpoints['quick_search'],
                method="POST",
                data=payload
            )
            
            # Parse and normalize response
            creators = self._parse_quick_search_response(response_data)
            
            # Cache the result
            if use_cache:
                await self._cache_result(cache_key, creators, self.cache_ttl['quick_search'])
            
            self.logger.info(f"Quick search completed: {len(creators)} creators found for '{query}'")
            return creators
            
        except Exception as e:
            self.logger.error(f"Quick search failed: {str(e)}")
            raise ExternalAPIError(f"Phyllo quick search failed: {str(e)}")
    
    @with_trace_id
    async def advanced_search(
        self,
        filters: DiscoveryFilters,
        platform: PlatformTypeEnum,
        page: int = 1,
        page_size: int = 20,
        use_cache: bool = True
    ) -> Tuple[List[Dict[str, Any]], Dict[str, Any]]:
        """
        Phyllo Advanced Search API - Detailed filtering capabilities
        https://docs.getphyllo.com/docs/api-reference/api/ref/operations/create-a-v-1-social-creator-profile-search
        """
        try:
            # Generate cache key
            cache_key = self._generate_cache_key('advanced_search', {
                'filters': filters.dict(),
                'platform': platform.value,
                'page': page,
                'page_size': page_size
            })
            
            # Try cache first
            if use_cache:
                cached_result = await self._get_from_cache(cache_key)
                if cached_result:
                    self.logger.info(f"Advanced search cache hit")
                    return cached_result['creators'], cached_result['metadata']
            
            # Convert filters to Phyllo format
            phyllo_filters = self._convert_filters_to_phyllo(filters, platform)
            
            # Prepare request payload
            payload = {
                "platform": platform.value,
                "filters": phyllo_filters,
                "pagination": {
                    "page": page,
                    "limit": page_size
                },
                "include_metrics": True,
                "include_demographics": True,
                "include_audience_insights": True
            }
            
            # Make API request
            response_data = await self._make_api_request(
                endpoint=self.endpoints['advanced_search'],
                method="POST",
                data=payload
            )
            
            # Parse response
            creators, metadata = self._parse_advanced_search_response(response_data)
            
            # Cache the result
            if use_cache:
                cache_data = {'creators': creators, 'metadata': metadata}
                await self._cache_result(cache_key, cache_data, self.cache_ttl['advanced_search'])
            
            self.logger.info(f"Advanced search completed: {len(creators)} creators found")
            return creators, metadata
            
        except Exception as e:
            self.logger.error(f"Advanced search failed: {str(e)}")
            raise ExternalAPIError(f"Phyllo advanced search failed: {str(e)}")
    
    @with_trace_id
    async def get_profile_analytics(
        self,
        creator_id: str,
        platform: PlatformTypeEnum,
        include_content: bool = True,
        include_audience: bool = True,
        date_range: Optional[str] = None,
        use_cache: bool = True
    ) -> Dict[str, Any]:
        """
        Phyllo Profile Analytics API - Comprehensive analytics data
        https://docs.getphyllo.com/docs/api-reference/api/ref/operations/create-a-v-1-social-creator-profile-analytics
        """
        try:
            # Generate cache key
            cache_key = self._generate_cache_key('profile_analytics', {
                'creator_id': creator_id,
                'platform': platform.value,
                'include_content': include_content,
                'include_audience': include_audience,
                'date_range': date_range
            })
            
            # Try cache first
            if use_cache:
                cached_result = await self._get_from_cache(cache_key)
                if cached_result:
                    self.logger.info(f"Profile analytics cache hit for creator: {creator_id}")
                    return cached_result
            
            # Prepare request payload
            payload = {
                "creator_id": creator_id,
                "platform": platform.value,
                "include_content_analysis": include_content,
                "include_audience_insights": include_audience,
                "include_performance_history": True,
                "include_top_content": True,
                "include_recent_content": True,
                "include_sponsored_content": True
            }
            
            if date_range:
                payload["date_range"] = date_range
            
            # Make API request
            response_data = await self._make_api_request(
                endpoint=self.endpoints['profile_analytics'],
                method="POST",
                data=payload
            )
            
            # Parse response (already in the format we need based on profile_analytics.json)
            analytics_data = self._parse_profile_analytics_response(response_data)
            
            # Cache the result
            if use_cache:
                await self._cache_result(cache_key, analytics_data, self.cache_ttl['profile_analytics'])
            
            self.logger.info(f"Profile analytics completed for creator: {creator_id}")
            return analytics_data
            
        except Exception as e:
            self.logger.error(f"Profile analytics failed for {creator_id}: {str(e)}")
            raise ExternalAPIError(f"Phyllo profile analytics failed: {str(e)}")
    
    async def _get_access_token(self) -> str:
        """Get or refresh access token for Phyllo API"""
        try:
            # Check if current token is still valid
            if (self._access_token and self._token_expires_at and 
                datetime.utcnow() < self._token_expires_at - timedelta(minutes=5)):
                return self._access_token
            
            # Check cache first
            cached_token = await self.redis.get("phyllo_access_token")
            if cached_token:
                token_data = json.loads(cached_token)
                self._access_token = token_data['access_token']
                self._token_expires_at = datetime.fromisoformat(token_data['expires_at'])
                return self._access_token
            
            # Request new token
            auth_payload = {
                "client_id": self.client_id,
                "client_secret": self.client_secret,
                "grant_type": "client_credentials"
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.base_url}{self.endpoints['auth']}",
                    json=auth_payload,
                    timeout=30.0
                )
                
                if response.status_code != 200:
                    raise CreatorVerseError(f"Phyllo authentication failed: {response.text}")
                
                token_data = response.json()
                self._access_token = token_data['access_token']
                expires_in = token_data.get('expires_in', 3600)
                self._token_expires_at = datetime.utcnow() + timedelta(seconds=expires_in)
                
                # Cache the token
                cache_data = {
                    'access_token': self._access_token,
                    'expires_at': self._token_expires_at.isoformat()
                }
                await self.redis.set(
                    "phyllo_access_token",
                    json.dumps(cache_data),
                    expire=expires_in - 300  # Expire 5 minutes early
                )
                
                return self._access_token
                
        except Exception as e:
            self.logger.error(f"Token acquisition failed: {str(e)}")
            raise CreatorVerseError(f"Failed to get Phyllo access token: {str(e)}")
    
    async def _make_api_request(
        self,
        endpoint: str,
        method: str = "POST",
        data: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Make authenticated request to Phyllo API"""
        try:
            # Get access token
            access_token = await self._get_access_token()
            
            headers = {
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            }
            
            async with httpx.AsyncClient() as client:
                if method.upper() == "POST":
                    response = await client.post(
                        f"{self.base_url}{endpoint}",
                        json=data,
                        headers=headers,
                        params=params,
                        timeout=30.0
                    )
                else:
                    response = await client.get(
                        f"{self.base_url}{endpoint}",
                        headers=headers,
                        params=params,
                        timeout=30.0
                    )
                
                if response.status_code == 429:
                    retry_after = int(response.headers.get('Retry-After', 60))
                    raise CreatorVerseError(f"Phyllo API rate limit exceeded. Retry after {retry_after} seconds")
                
                if response.status_code not in [200, 201]:
                    self.logger.error(f"Phyllo API error: {response.status_code} - {response.text}")
                    raise CreatorVerseError(f"Phyllo API request failed: {response.text}")
                
                return response.json()
                
        except httpx.TimeoutException:
            raise CreatorVerseError("Phyllo API request timed out")
        except Exception as e:
            if isinstance(e, CreatorVerseError):
                raise
            self.logger.error(f"Error making Phyllo API request: {str(e)}")
            raise CreatorVerseError(f"Failed to communicate with Phyllo API: {str(e)}")
    
    def _generate_cache_key(self, endpoint: str, params: Dict[str, Any]) -> str:
        """Generate cache key for request"""
        params_str = json.dumps(params, sort_keys=True)
        hash_value = hashlib.md5(params_str.encode()).hexdigest()
        return f"phyllo:{endpoint}:{hash_value}"
    
    async def _get_from_cache(self, cache_key: str) -> Optional[Any]:
        """Get cached result"""
        try:
            cached_data = await self.redis.get(cache_key)
            if cached_data:
                return json.loads(cached_data)
        except Exception as e:
            self.logger.warning(f"Cache retrieval failed: {e}")
        return None
    
    async def _cache_result(self, cache_key: str, data: Any, ttl: int):
        """Cache API result"""
        try:
            await self.redis.set(cache_key, json.dumps(data), expire=ttl)
        except Exception as e:
            self.logger.warning(f"Cache storage failed: {e}")
    
    def _parse_quick_search_response(self, response_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Parse quick search response to normalized format"""
        creators = []
        for creator_data in response_data.get('data', []):
            normalized_creator = {
                'external_id': creator_data.get('id'),
                'platform_username': creator_data.get('username'),
                'full_name': creator_data.get('display_name'),
                'follower_count': creator_data.get('follower_count'),
                'engagement_rate': creator_data.get('engagement_rate'),
                'image_url': creator_data.get('profile_picture_url'),
                'is_verified': creator_data.get('is_verified', False),
                'platform': creator_data.get('platform'),
                'location': creator_data.get('location', {}).get('city'),
                'category': creator_data.get('primary_category')
            }
            creators.append(normalized_creator)
        return creators
    
    def _parse_advanced_search_response(self, response_data: Dict[str, Any]) -> Tuple[List[Dict[str, Any]], Dict[str, Any]]:
        """Parse advanced search response"""
        creators = self._parse_quick_search_response(response_data)
        
        metadata = {
            'total_count': response_data.get('total_count', len(creators)),
            'page': response_data.get('page', 1),
            'page_size': response_data.get('page_size', len(creators)),
            'has_next': response_data.get('has_next', False)
        }
        
        return creators, metadata
    
    def _parse_profile_analytics_response(self, response_data: Dict[str, Any]) -> Dict[str, Any]:
        """Parse profile analytics response (already in correct format)"""
        return response_data
    
    def _convert_filters_to_phyllo(self, filters: DiscoveryFilters, platform: PlatformTypeEnum) -> Dict[str, Any]:
        """Convert our filter format to Phyllo API format"""
        phyllo_filters = {}
        
        if filters.demographic:
            demo = filters.demographic
            if demo.gender:
                phyllo_filters['creator_gender'] = demo.gender
            if demo.age_group:
                phyllo_filters['creator_age'] = demo.age_group
            if demo.country:
                phyllo_filters['creator_locations'] = demo.country
        
        if filters.performance:
            perf = filters.performance
            if perf.follower_count:
                phyllo_filters['follower_count'] = {
                    'min': perf.follower_count.min_value,
                    'max': perf.follower_count.max_value
                }
            if perf.engagement_rate:
                phyllo_filters['engagement_rate'] = {
                    'min': perf.engagement_rate.min_value,
                    'max': perf.engagement_rate.max_value
                }
        
        if filters.content:
            content = filters.content
            if content.interests:
                phyllo_filters['creator_interests'] = content.interests
        
        return phyllo_filters
