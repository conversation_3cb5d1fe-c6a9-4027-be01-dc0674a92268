"""
Filter system models for CreatorVerse Discovery Profile Analytics.

This module contains SQLAlchemy models for the comprehensive filter system
that supports creator and audience discovery across multiple platforms.
"""

import enum
import uuid
from datetime import datetime
from typing import List, Dict, Any, Optional

from sqlalchemy import (
    Column, String, Integer, Boolean, SmallInteger, DateTime, UniqueConstraint, 
    text, func, Text, ForeignKey, JSON
)
from sqlalchemy.dialects.postgresql import UUID, JSONB, ENUM as PGEnum
from sqlalchemy.orm import declarative_base, relationship

Base = declarative_base()


class DataTypeEnum(enum.Enum):
    """Enum for data types that filters can handle."""
    range = "range"
    enum = "enum"
    multi_enum = "multi_enum"
    boolean = "boolean"
    keyword = "keyword"
    location = "location"
    date_range = "date_range"


class FilterTypeEnum(enum.Enum):
    """Enum for UI filter component types."""
    radio_button = "radio_button"
    checkbox = "checkbox"
    multilevel_checkbox = "multilevel_checkbox"
    enter_value = "enter_value"
    range_slider = "range_slider"
    date_picker = "date_picker"
    search_dropdown = "search_dropdown"


class OptionForTypeEnum(enum.Enum):
    """Enum for what the filter is targeting (creator or audience)."""
    creator = "creator"
    audience = "audience"


class PlatformTypeEnum(enum.Enum):
    """Enum for supported social media platforms."""
    instagram = "instagram"
    youtube = "youtube"
    tiktok = "tiktok"


class ProviderEnum(enum.Enum):
    """Enum for data providers."""
    phyllo_v1 = "phyllo_v1"
    phyllo_v2 = "phyllo_v2"
    modash = "modash"
    custom = "custom"


class ValidationTypeEnum(enum.Enum):
    """Enum for validation types."""
    none = "none"
    min_max = "min_max"
    regex = "regex"
    length = "length"
    custom = "custom"


class FilterGroup(Base):
    """
    Filter groups organize filters by logical categories.
    
    Examples: 'Demography & Identity', 'Performance Metrics', 'Content & Niche'
    """
    __tablename__ = 'filter_groups'
    __table_args__ = (
        UniqueConstraint('name', 'option_for', 'channel', name='uq_filter_group_context'),
        {'schema': 'filter_catalog'}
    )

    id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4,
        server_default=text('gen_random_uuid()'),
        nullable=False
    )
    name = Column(
        String(100),
        nullable=False,
        comment="Display name of the filter group"
    )
    option_for = Column(
        PGEnum(
            OptionForTypeEnum,
            name="option_for_type",
            schema="filter_catalog",
            validate_strings=False,
        ),
        nullable=False,
        server_default=OptionForTypeEnum.creator.value,
        comment="Whether this group is for creator or audience filters"
    )
    channel = Column(
        PGEnum(
            PlatformTypeEnum,
            name="platform_type",
            schema="filter_catalog",
            
            validate_strings=False,
        ),
        nullable=False,
        server_default=PlatformTypeEnum.instagram.value,
        comment="Platform this group applies to"
    )
    sort_order = Column(
        SmallInteger,
        nullable=True,
        server_default=text('0'),
        comment="Display order for UI"
    )
    is_active = Column(
        Boolean,
        nullable=False,
        server_default=text('true'),
        comment="Whether this group is active and should be shown"
    )
    created_at = Column(
        DateTime(timezone=True),
        nullable=False,
        server_default=func.now()
    )
    updated_at = Column(
        DateTime(timezone=True),
        nullable=False,
        server_default=func.now(),
        onupdate=func.now()
    )

    # Relationships
    filter_definitions = relationship(
        "FilterDefinition",
        back_populates="group",
        cascade="all, delete-orphan"
    )

    def __repr__(self) -> str:
        return (
            f"<FilterGroup(name='{self.name}', option_for={self.option_for.value}, "
            f"channel={self.channel.value}, active={self.is_active})>"
        )

    @property
    def active_filters(self) -> List["FilterDefinition"]:
        """Get all active filter definitions for this group."""
        return [f for f in self.filter_definitions if f.is_active]


class FilterDefinition(Base):
    """
    Individual filter definitions with their UI configuration and API mappings.
    """
    __tablename__ = 'filter_definitions'
    __table_args__ = (
        UniqueConstraint('group_id', 'name', name='uq_filter_group_name'),
        {'schema': 'filter_catalog'}
    )

    id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4,
        server_default=text('gen_random_uuid()'),
        nullable=False
    )
    group_id = Column(
        UUID(as_uuid=True),
        ForeignKey('filter_catalog.filter_groups.id', ondelete='CASCADE'),
        nullable=False,
        comment="Reference to the filter group"
    )
    name = Column(
        String(100),
        nullable=False,
        comment="Display name of the filter"
    )
    filter_type = Column(
        PGEnum(
            FilterTypeEnum,
            name="filter_type",
            schema="filter_catalog",
            validate_strings=True,
        ),
        nullable=False,
        server_default=FilterTypeEnum.checkbox.value,
        comment="UI component type for this filter"
    )
    icon = Column(
        String(50),
        nullable=True,
        comment="Icon name or class for UI display"
    )
    has_minmax = Column(
        Boolean,
        nullable=False,
        server_default=text('false'),
        comment="Whether this filter supports min/max input"
    )
    has_enter_value = Column(
        Boolean,
        nullable=False,
        server_default=text('false'),
        comment="Whether this filter allows free text input"
    )
    has_search_box = Column(
        Boolean,
        nullable=False,
        server_default=text('false'),
        comment="Whether this filter has a search functionality"
    )
    placeholder = Column(
        String(200),
        nullable=True,
        comment="Placeholder text for input fields"
    )
    options = Column(
        JSONB,
        nullable=False,
        server_default=text("'[]'::jsonb"),
        comment="JSON array of filter options with label, value, description"
    )
    db_field = Column(
        String(100),
        nullable=True,
        comment="Database field name for this filter"
    )
    api_field = Column(
        String(100),
        nullable=True,
        comment="API field name for external provider calls"
    )
    sort_order = Column(
        SmallInteger,
        nullable=True,
        server_default=text('0'),
        comment="Display order within the group"
    )
    is_active = Column(
        Boolean,
        nullable=False,
        server_default=text('true'),
        comment="Whether this filter is active and should be shown"
    )
    created_at = Column(
        DateTime(timezone=True),
        nullable=False,
        server_default=func.now()
    )
    updated_at = Column(
        DateTime(timezone=True),
        nullable=False,
        server_default=func.now(),
        onupdate=func.now()
    )

    # Relationships
    group = relationship(
        "FilterGroup",
        back_populates="filter_definitions"
    )

    def __repr__(self) -> str:
        return (
            f"<FilterDefinition(name='{self.name}', type={self.filter_type.value}, "
            f"group_id={self.group_id}, active={self.is_active})>"
        )

    @property
    def is_multi_option(self) -> bool:
        """Check if this filter supports multiple selections."""
        return self.filter_type in [
            FilterTypeEnum.checkbox,
            FilterTypeEnum.multilevel_checkbox,
            FilterTypeEnum.search_dropdown,
        ]

    @property
    def requires_input(self) -> bool:
        """Check if this filter requires user input."""
        return self.filter_type in [
            FilterTypeEnum.enter_value,
            FilterTypeEnum.range_slider,
            FilterTypeEnum.date_picker
        ]


class LocationHierarchy(Base):
    """
    Hierarchical location data with tier-based classification.
    Supports country > state > city hierarchy with tier information.
    """
    __tablename__ = 'location_hierarchy'
    __table_args__ = (
        UniqueConstraint('name', 'parent_id', 'level', name='uq_location_context'),
        {'schema': 'filter_catalog'}
    )

    id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4,
        server_default=text('gen_random_uuid()'),
        nullable=False
    )
    name = Column(
        String(100),
        nullable=False,
        comment="Location name (e.g., Mumbai, Maharashtra, India)"
    )
    code = Column(
        String(20),
        nullable=True,
        comment="Location code (e.g., MUM, MH, IN)"
    )
    level = Column(
        Integer,
        nullable=True,
        server_default=text('0'),
        comment="Hierarchy level: 0=country, 1=state, 2=city"
    )
    parent_id = Column(
        UUID(as_uuid=True),
        ForeignKey('filter_catalog.location_hierarchy.id', ondelete='CASCADE'),
        nullable=True,
        comment="Parent location reference"
    )
    population = Column(
        Integer,
        nullable=True,
        comment="Population count for demographic insights"
    )
    tier = Column(
        String(10),
        nullable=True,
        comment="Tier classification (Tier 1, Tier 2, Tier 3, Rural)"
    )
    is_active = Column(
        Boolean,
        nullable=False,
        server_default=text('true'),
        comment="Whether this location is active for selection"
    )
    created_at = Column(
        DateTime(timezone=True),
        nullable=False,
        server_default=func.now()
    )
    updated_at = Column(
        DateTime(timezone=True),
        nullable=False,
        server_default=func.now(),
        onupdate=func.now()
    )

    # Self-referential relationship
    parent = relationship(
        "LocationHierarchy",
        remote_side=[id],
        back_populates="children"
    )
    children = relationship(
        "LocationHierarchy",
        back_populates="parent",
        cascade="all, delete-orphan"
    )

    def __repr__(self) -> str:
        return (
            f"<LocationHierarchy(name='{self.name}', level={self.level}, "
            f"tier='{self.tier}', active={self.is_active})>"
        )

    @property
    def full_path(self) -> str:
        """Get the full hierarchical path (e.g., 'Mumbai, Maharashtra, India')."""
        path_parts = [self.name]
        current = self.parent
        while current:
            path_parts.append(current.name)
            current = current.parent
        return ", ".join(reversed(path_parts))

    @property
    def is_city(self) -> bool:
        """Check if this is a city-level location."""
        return self.level == 2

    @property
    def is_state(self) -> bool:
        """Check if this is a state-level location."""
        return self.level == 1

    @property
    def is_country(self) -> bool:
        """Check if this is a country-level location."""
        return self.level == 0


class SavedFilterSet(Base):
    """
    User-created filter combinations that can be saved, shared, and reused.
    """
    __tablename__ = 'saved_filter_sets'
    __table_args__ = (
        UniqueConstraint('share_code', name='uq_share_code'),
        {'schema': 'filter_catalog'}
    )

    id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4,
        server_default=text('gen_random_uuid()'),
        nullable=False
    )
    name = Column(
        String(100),
        nullable=False,
        comment="User-defined name for this filter set"
    )
    channel = Column(
        PGEnum(
            PlatformTypeEnum,
            name="platform_type",
            schema="filter_catalog",
            
            validate_strings=False,
        ),
        nullable=False,
        server_default=PlatformTypeEnum.instagram.value,
        comment="Platform this filter set applies to"
    )
    option_for = Column(
        PGEnum(
            OptionForTypeEnum,
            name="option_for_type",
            schema="filter_catalog",
            
            validate_strings=False,
        ),
        nullable=False,
        server_default=OptionForTypeEnum.creator.value,
        comment="Whether this filter set is for creator or audience"
    )
    filter_values = Column(
        JSONB,
        nullable=False,
        comment="JSON object containing the actual filter values and selections"
    )
    user_id = Column(
        UUID(as_uuid=True),
        nullable=True,
        comment="User who created this filter set (null for anonymous/shared sets)"
    )
    is_shared = Column(
        Boolean,
        nullable=False,
        server_default=text('false'),
        comment="Whether this filter set is publicly shared"
    )
    share_code = Column(
        String(50),
        nullable=True,
        unique=True,
        comment="Unique code for sharing this filter set"
    )
    usage_count = Column(
        Integer,
        nullable=False,
        server_default=text('0'),
        comment="Number of times this filter set has been used"
    )
    created_at = Column(
        DateTime(timezone=True),
        nullable=False,
        server_default=func.now()
    )
    updated_at = Column(
        DateTime(timezone=True),
        nullable=False,
        server_default=func.now(),
        onupdate=func.now()
    )

    def __repr__(self) -> str:
        return (
            f"<SavedFilterSet(name='{self.name}', channel={self.channel.value}, "
            f"shared={self.is_shared}, usage={self.usage_count})>"
        )

    @property
    def is_anonymous(self) -> bool:
        """Check if this filter set was created by an anonymous user."""
        return self.user_id is None

    def increment_usage(self) -> None:
        """Increment the usage count for analytics."""
        self.usage_count += 1


class FilterUsageLog(Base):
    """
    Analytics table to track filter usage patterns and performance.
    """
    __tablename__ = 'filter_usage_logs'
    __table_args__ = {'schema': 'filter_catalog'}

    id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4,
        server_default=text('gen_random_uuid()'),
        nullable=False
    )
    filter_definition_id = Column(
        UUID(as_uuid=True),
        ForeignKey('filter_catalog.filter_definitions.id', ondelete='CASCADE'),
        nullable=False,
        comment="Reference to the filter that was used"
    )
    user_id = Column(
        UUID(as_uuid=True),
        nullable=True,
        comment="User who applied the filter (null for anonymous users)"
    )
    session_id = Column(
        String(255),
        nullable=True,
        comment="Session identifier for anonymous usage tracking"
    )
    filter_value = Column(
        JSONB,
        nullable=True,
        comment="The actual value(s) selected for this filter"
    )
    result_count = Column(
        Integer,
        nullable=True,
        comment="Number of results returned for this filter application"
    )
    execution_time_ms = Column(
        Integer,
        nullable=True,
        comment="Time taken to execute the filter in milliseconds"
    )
    created_at = Column(
        DateTime(timezone=True),
        nullable=False,
        server_default=func.now(),
        comment="When this filter was applied"
    )

    # Relationships
    filter_definition = relationship(
        "FilterDefinition",
        foreign_keys=[filter_definition_id]
    )

    def __repr__(self) -> str:
        return (
            f"<FilterUsageLog(filter_id={self.filter_definition_id}, "
            f"result_count={self.result_count}, created_at={self.created_at})>"
        )


# Index creation for performance optimization
from sqlalchemy import Index

# Indexes for filter_groups
Index('idx_filter_groups_active', FilterGroup.is_active, FilterGroup.channel, FilterGroup.option_for)
Index('idx_filter_groups_sort', FilterGroup.channel, FilterGroup.option_for, FilterGroup.sort_order)

# Indexes for filter_definitions  
Index('idx_filter_definitions_active', FilterDefinition.group_id, FilterDefinition.is_active)
Index('idx_filter_definitions_sort', FilterDefinition.group_id, FilterDefinition.sort_order)
Index('idx_filter_definitions_type', FilterDefinition.filter_type)

# Indexes for location_hierarchy
Index('idx_location_hierarchy_level', LocationHierarchy.level, LocationHierarchy.is_active)
Index('idx_location_hierarchy_parent', LocationHierarchy.parent_id, LocationHierarchy.is_active)
Index('idx_location_hierarchy_tier', LocationHierarchy.tier, LocationHierarchy.is_active)

# Indexes for saved_filter_sets
Index('idx_saved_filter_sets_user', SavedFilterSet.user_id, SavedFilterSet.channel)
Index('idx_saved_filter_sets_shared', SavedFilterSet.is_shared, SavedFilterSet.channel)

# Indexes for filter_usage_logs
Index('idx_filter_usage_logs_filter', FilterUsageLog.filter_definition_id, FilterUsageLog.created_at)
Index('idx_filter_usage_logs_user', FilterUsageLog.user_id, FilterUsageLog.created_at)
Index('idx_filter_usage_logs_session', FilterUsageLog.session_id, FilterUsageLog.created_at)
