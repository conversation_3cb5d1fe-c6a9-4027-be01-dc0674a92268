"""
API router for CreatorVerse Discovery & Analytics Backend
"""
from fastapi import APIRouter

from app.api.api_v1.endpoints import discovery, filters, health, frontend_discovery

# Create main API router
api_router = APIRouter()

api_router.include_router(health.router, prefix="/health", tags=["health"])
api_router.include_router(discovery.router, prefix="/discovery", tags=["discovery"])
api_router.include_router(filters.router, prefix="/filters", tags=["filters"])
api_router.include_router(frontend_discovery.router, prefix="/frontend", tags=["frontend-discovery"])
